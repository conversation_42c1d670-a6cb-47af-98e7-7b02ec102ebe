import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:xinglian/src/features/chat/models/message_model.dart';
import 'package:xinglian/src/features/chat/repository/chat_repository.dart';
import 'package:xinglian/src/features/discovery/models/agent_model.dart';
import 'package:xinglian/src/features/interactive_story/models/story_models.dart' as story_models; // 引入故事模型
import 'package:xinglian/src/features/interactive_story/repository/story_repository.dart'; // 引入故事仓库
import 'package:xinglian/src/features/chat/models/choice_model.dart';

part 'chat_player_event.dart';
part 'chat_player_state.dart';

class ChatPlayerBloc extends Bloc<ChatPlayerEvent, ChatPlayerState> {
  final ChatRepository _chatRepository;
  final StoryRepository _storyRepository; // 新增故事仓库依赖
  StreamSubscription? _chatSubscription;
  ChatChannel? _chatChannel;

  ChatPlayerBloc({
    required ChatRepository chatRepository,
    required StoryRepository storyRepository, // 修改构造函数
  })  : _chatRepository = chatRepository,
        _storyRepository = storyRepository,
        super(const ChatPlayerState()) {
    // --- 原有事件处理器 ---
    on<ConnectToChat>(_onConnectToChat);
    on<SendMessage>(_onSendMessage);
    on<_MessageReceived>(_onMessageReceived);
    on<_ConnectionClosed>(_onConnectionClosed);
    on<_GameStateSyncReceived>(_onGameStateSyncReceived);
    on<SwitchTargetAgent>(_onSwitchTargetAgent);
    on<StartChapterIntro>(_onStartChapterIntro);
    on<DismissChapterIntro>(_onDismissChapterIntro);
    on<ShowNextStoryElement>(_onShowNextStoryElement);
    on<CompleteProgressiveStory>(_onCompleteProgressiveStory);

    // --- vvv 迁移并注册来自 StoryPlayerBloc 的事件处理器 vvv ---
    on<TapToContinue>(_onTapToContinue);
    on<MakeChoice>(_onMakeChoice);
    on<ContinueAfterChapter>(_onContinueAfterChapter);
    on<ContinueCurrentChapter>(_onContinueCurrentChapter);
    on<LoadNextChapter>(_onLoadNextChapter);
    on<_NextChapterLoaded>(_onNextChapterLoaded);
    on<_NodeReceived>(_onNodeReceived);
    on<_ScoreUpdated>(_onScoreUpdated);
    on<_LoadingReceived>(_onLoadingReceived);
    on<_ChapterCompleted>(_onChapterCompleted);
    on<_EnterChatScene>(_onEnterChatScene);
    on<_GameStateChanged>(_onGameStateChanged);
    on<LoadMoreMessages>(_onLoadMoreMessages);
    on<RegenerateChoicesRequested>(_onRegenerateChoicesRequested);
    on<ToggleChoicesExpansion>(_onToggleChoicesExpansion);
    on<ToggleChoicesPanelVisibility>(_onToggleChoicesPanelVisibility);
  }

  Future<void> _onConnectToChat(
    ConnectToChat event,
    Emitter<ChatPlayerState> emit,
  ) async {
    emit(state.copyWith(status: ChatPlayerStatus.loading, chatId: event.chatId, userId: event.userId));
    try {
      // 检查这是一个故事聊天还是普通聊天
      final chatSessionDetails = await _chatRepository.getChatSessionDetails(event.chatId);
      final storyId = chatSessionDetails?['story_id'] as String?;

      print('=== ChatPlayerBloc Debug ===');
      print('Chat ID: ${event.chatId}');
      print('Story ID: $storyId');
      print('Is Story Mode: ${storyId != null}');

      // 并行获取通用数据
      final results = await Future.wait([
        _chatRepository.getMessages(event.chatId),
        _chatRepository.getParticipants(event.chatId),
        // 如果是故事，则额外获取故事详情
        if (storyId != null) _storyRepository.fetchStoryDetail(storyId),
      ]);

      // 修复：为所有从数据库加载的初始消息添加 isHistory 标记
      final initialMessages = results[0] as List<Message>;
      final messages = initialMessages.map((m) => m.copyWith(metadata: {...(m.metadata ?? {}), 'isHistory': true})).toList();
      final participants = results[1] as List<Agent>;
      final storyDetail = storyId != null ? results[2] as story_models.InteractiveStoryDetail : null;
      final taskProgress = storyId != null ? (chatSessionDetails?['task_progress'] as Map<String, dynamic>?) : null;

      print('Loaded ${messages.length} existing messages from database');
      for (int i = 0; i < messages.length && i < 5; i++) {
        final content = messages[i].content;
        final preview = content.length > 50 ? content.substring(0, 50) : content;
        print('Message $i: ${messages[i].role} - $preview...');
      }

      // --- 调试代码：检查participants ---
      print('--- PARTICIPANTS DEBUG ---');
      print('Loaded ${participants.length} participants:');
      for (var participant in participants) {
        print('  - ID: ${participant.id}, Name: ${participant.name}');
      }
      print('-------------------------');

      // ... (设置 targetAgent 的逻辑保持不变)
      Agent? lastSpeakingAgent;
      for (var i = messages.length - 1; i >= 0; i--) {
        final message = messages[i];
        if (message.role == MessageRole.assistant && message.agentId != null) {
          try {
            lastSpeakingAgent = participants.firstWhere((p) => p.id == message.agentId);
            break;
          } catch (e) {/* 忽略 */}
        }
      }
      final targetAgent = lastSpeakingAgent ?? (participants.isNotEmpty ? participants.first : null);

      // PRD要求：如果已有消息历史，说明开场演绎已完成，应设置为gameplay模式
      final initialStoryState = (storyDetail != null && messages.isNotEmpty)
        ? StoryInteractionState.gameplay
        : StoryInteractionState.cinematic;

      // 修复：从taskProgress中提取当前进度值
      int currentProgress = 0;
      if (taskProgress != null && storyDetail != null) {
        final currentChapterId = taskProgress['current_chapter_id'] as String?;
        if (currentChapterId != null) {
          final chapters = taskProgress['chapters'] as Map<String, dynamic>?;
          if (chapters != null && chapters.containsKey(currentChapterId)) {
            final chapterProgress = chapters[currentChapterId] as Map<String, dynamic>?;
            currentProgress = chapterProgress?['progress'] as int? ?? 0;
          }
        } else if (storyDetail.chapters.isNotEmpty) {
          // 如果没有current_chapter_id，使用第一个章节的进度
          final firstChapterId = storyDetail.chapters.first.id;
          final chapters = taskProgress['chapters'] as Map<String, dynamic>?;
          if (chapters != null && chapters.containsKey(firstChapterId)) {
            final chapterProgress = chapters[firstChapterId] as Map<String, dynamic>?;
            currentProgress = chapterProgress?['progress'] as int? ?? 0;
          }
        }
      }

      print('=== Progress Loading Debug ===');
      print('TaskProgress: $taskProgress');
      print('Loaded currentProgress: $currentProgress');

      // === [新增] 明确获取并设置主角Agent信息 ===
      Agent? protagonistAgent;
      if (storyDetail != null && storyDetail.protagonistAgentId != null) {
          try {
              // 从故事详情附带的角色列表中查找主角
              protagonistAgent = storyDetail.agents.firstWhere((agent) => agent.id == storyDetail.protagonistAgentId);
               print('Protagonist Agent Found: ${protagonistAgent.name}');
          } catch (e) {
              print('ERROR: Protagonist Agent with ID ${storyDetail.protagonistAgentId} not found in story agents list.');
          }
      }
      // === [新增结束] ===

      emit(state.copyWith(
        messages: messages,
        participants: participants,
        currentTargetAgent: targetAgent,
        storyDetail: storyDetail, // 更新 state
        protagonistAgent: protagonistAgent, // <--- 传递主角信息
        taskProgress: taskProgress, // 更新 state
        currentProgress: currentProgress, // 修复：设置从数据库加载的进度值
        storyInteractionState: initialStoryState, // 根据消息历史设置初始状态
      ));

      // 连接 WebSocket - 修复：不再需要 storyId 参数
      _chatChannel = _chatRepository.connectToChat(event.chatId, event.userId);
      // 修复：简化WebSocket监听器，只负责接收消息并转发给BLoC
      _chatSubscription?.cancel(); // 先取消旧的订阅
      _chatSubscription = _chatChannel!.stream.listen(
        (message) {
          print('=== ChatPlayerBloc Message Processing ===');
          print('Message type: ${message is Map ? message['type'] : message.runtimeType}');
          
          // *** 核心修复：这里只做一件事，就是把收到的任何消息都发给 BLoC 处理 ***
          add(_MessageReceived(message));
        },
        onDone: () => add(_ConnectionClosed()),
        onError: (error) => print('ChatPlayerBloc stream error: $error'),
      );
      
      emit(state.copyWith(status: ChatPlayerStatus.success));

      // 检查开场序列是否已经开始
      final openingSequenceIndex = taskProgress?['opening_sequence_index'] as int? ?? 0;
      final hasOpeningSequenceStarted = openingSequenceIndex > 0;

      // 如果是故事模式且开场序列未开始，显示章节介绍
      if (storyDetail != null && !hasOpeningSequenceStarted) {
        print('Story mode detected, starting chapter intro');

        // 从后端获取当前章节的动态介绍内容
        List<Map<String, dynamic>> chapterElements = [];

        try {
          // 尝试从故事详情中获取章节介绍元素
          final currentChapter = storyDetail.chapters.isNotEmpty ? storyDetail.chapters.first : null;
          if (currentChapter != null && currentChapter.introElements != null) {
            chapterElements = List<Map<String, dynamic>>.from(currentChapter.introElements!);
          } else {
            // 如果没有预定义的介绍元素，使用默认的章节描述
            chapterElements = [
              {
                "element_type": "text",
                "content_or_prompt": currentChapter?.description ?? storyDetail.description ?? "准备开始你的冒险..."
              }
            ];
          }
        } catch (e) {
          print('Error loading chapter intro elements: $e');
          // 使用故事描述作为后备
          chapterElements = [
            {
              "element_type": "text",
              "content_or_prompt": storyDetail.description ?? "准备开始你的冒险..."
            }
          ];
        }

        add(StartChapterIntro(chapterElements));
      } else if (storyDetail != null && hasOpeningSequenceStarted) {
        // 如果开场序列已经开始，直接进入游戏模式，不显示章节介绍
        print('Opening sequence already started (index: $openingSequenceIndex), skipping chapter intro');
        emit(state.copyWith(
          storyInteractionState: StoryInteractionState.gameplay,
          showChapterIntro: false,
        ));
      }

    } catch (e) {
      emit(state.copyWith(status: ChatPlayerStatus.failure, errorMessage: e.toString()));
    }
  }

  // 其他事件处理器保持不变...
  void _onSendMessage(SendMessage event, Emitter<ChatPlayerState> emit,) {
    if (state.status == ChatPlayerStatus.success) {
      // 1. 创建用户的消息对象
      final userMessage = Message(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        chatId: state.chatId ?? '',
        content: event.content,
        role: MessageRole.user,
        createdAt: DateTime.now(),
      );
      
      // 2.【核心修改】第一步：立即发出只包含用户新消息的状态
      // 这将立刻在UI上渲染出用户的发言
      final messagesWithUserOnly = List<Message>.from(state.messages)
        ..add(userMessage);

      final hadChoices = state.currentChoices != null && state.currentChoices!.isNotEmpty;
      print('DEBUG: SendMessage - hadChoices: $hadChoices, clearing currentChoices');

      // --- ▼▼▼ 核心修正 ▼▼▼ ---
      // 如果用户刚刚做出了选择（hadChoices 为 true），并且是故事模式，
      // 那么应该进入一个等待AI响应的状态，而不是立即切换到cinematic模式
      final shouldHandleChoice = hadChoices && state.storyDetail != null;

      emit(state.copyWith(
        messages: messagesWithUserOnly,
        currentChoices: [], // 清空选项
        showChoicesPanel: false, // 核心修改：做出选择后，隐藏面板
        isReplying: true, // 立即显示"输入中"
        isGeneratingChoices: false, // 不再立即显示选项生成
        justMadeChoice: hadChoices, // 如果之前有选项，说明用户刚做了选择
        // 关键修复：做出选择后，不立即切换到cinematic，而是保持gameplay并显示加载中。
        // 等待后端推送下一个元素（旁白或对话）时，由 _onMessageReceived 处理器来决定是否切换到 cinematic。
        storyInteractionState: shouldHandleChoice
            ? StoryInteractionState.gameplay // <-- 保持Gameplay状态
            : state.storyInteractionState,
        // 不改变isProgressiveStoryMode，等待后端消息来决定
        isProgressiveStoryMode: state.isProgressiveStoryMode,
      ));
      // --- ▲▲▲ 核心修正 ▲▲▲ ---
      
      // 3. 通过WebSocket将用户的消息发送到后端
      _chatChannel?.sink(event.content,
        targetAgentId: event.targetAgentId,
      );
      
      // 4. 【可选优化】不再需要立即添加AI占位消息。
      // 流式消息的处理逻辑 (_onMessageReceived) 会在收到第一个 chunk 时创建AI消息。
      // 这使得逻辑更简单，完全由服务器驱动。
    }
  }

  void _onMessageReceived(_MessageReceived event, Emitter<ChatPlayerState> emit,) async {
    final message = event.message;
    
    // 1. 如果是 Message 类型，直接处理
    if (message is Message) {
      final messages = List<Message>.from(state.messages);
      
      // 防止重复添加用户消息（前端已经添加过了）
      // 修复：区分用户自己输入的消息和系统发送的主角演绎消息
      if (message.role == MessageRole.user && 
          !(message.metadata?.containsKey('is_opening_sequence') == true)) {
        print('Ignoring duplicate user message from WebSocket: ${message.content}');
        return; // 直接忽略从WebSocket接收到的用户消息
      } else {
        messages.add(message);
      }
      
      emit(state.copyWith(messages: messages, isReplying: false));
      return;
    }
    
    // 2. 如果是 Map 类型，说明是结构化消息，需要解析
    if (message is Map<String, dynamic>) {
      final messageType = message['type'];
      print('Processing WebSocket message of type: $messageType');
      
      switch (messageType) {
        case 'game_state_sync':
          // 处理后端发送的完整状态同步
          final data = message['data'] as Map<String, dynamic>;
          print('Received game_state_sync, processing state update');

          // 从同步数据中提取历史消息（避免重复显示打字机效果）
          final syncedMessages = (data['messages'] as List<dynamic>? ?? [])
              .map((msgData) {
                final msg = Message.fromJson(msgData);
                // 为历史消息添加元数据标记
                return msg.copyWith(
                  metadata: {...(msg.metadata ?? {}), 'isHistory': true}
                );
              })
              .toList();

          // 从WebSocket消息中提取participants数据
          final wsParticipants = (data['participants'] as List<dynamic>? ?? [])
              .map((participantData) => Agent.fromJson(participantData))
              .toList();

          // 处理主角信息
          Agent? protagonistAgent;
          final protagonistAgentId = data['protagonist_agent_id'] as String?;

          print('=== PROTAGONIST PROCESSING DEBUG ===');
          print('protagonistAgentId from WebSocket: $protagonistAgentId');
          print('WebSocket participants.length: ${wsParticipants.length}');
          print('WebSocket participants: ${wsParticipants.map((a) => '${a.name}(${a.id})').toList()}');
          print('state.participants.length: ${state.participants.length}');

          if (protagonistAgentId != null && wsParticipants.isNotEmpty) {
            try {
              protagonistAgent = wsParticipants.firstWhere(
                (agent) => agent.id == protagonistAgentId,
              );
              print('Found protagonist agent: ${protagonistAgent.name}');
            } catch (e) {
              print('Protagonist agent not found in WebSocket participants: $protagonistAgentId');
              print('Available WebSocket participants: ${wsParticipants.map((a) => '${a.name}(${a.id})').toList()}');
            }
          } else {
            print('Cannot process protagonist: protagonistAgentId=$protagonistAgentId, wsParticipants.length=${wsParticipants.length}');
          }
          print('=====================================');

          // 通过事件处理状态更新
          add(_GameStateSyncReceived(
            messages: syncedMessages,
            participants: wsParticipants,
            protagonistAgent: protagonistAgent,
            hasHistoryMessages: syncedMessages.isNotEmpty,
          ));
          break;
          
        case 'new_node':
          // 故事节点消息 - 转换为普通消息格式
          final nodeData = message['data'] as Map<String, dynamic>;
          print('Processing story node: $nodeData');
          
          // 创建消息对象
          final storyMessage = Message(
            id: 'story_node_${DateTime.now().millisecondsSinceEpoch}',
            chatId: state.chatId ?? '',
            content: nodeData['content_or_prompt'] ?? '',
            role: nodeData['character_id'] != null ? MessageRole.assistant : MessageRole.narration,
            agentId: nodeData['character_id'],
            createdAt: DateTime.now(),
          );
          
          add(_MessageReceived(storyMessage));
          break;
          
        case 'initial_state':
          // 故事初始状态 - 暂时忽略，因为我们使用不同的初始化方式
          print('Received initial_state, ignoring for now');
          break;
          
        case 'enter_chat_scene':
          // 进入聊天场景
          print('Received enter_chat_scene');
          // TODO: 处理进入聊天场景的逻辑
          break;
          
        case 'chapter_complete':
          // 章节完成
          print('Received chapter_complete');
          // TODO: 处理章节完成的逻辑
          break;
          
        case 'score_update':
          // PRD要求：实时评分反馈
          final increment = message['progress_increment'] as int? ?? 0;
          final newTotal = message['current_progress'] as int? ?? 0;
          final isChapterComplete = message['chapter_complete'] as bool? ?? false;

          add(_ScoreUpdated(
            increment: increment,
            newTotal: newTotal,
            isChapterComplete: isChapterComplete,
          ));
          break;

        case 'task_progress_update':
          // 任务进度更新（保留向后兼容）
          break;

        case 'opening_sequence_element':
          // 开场演绎元素 - 新增处理
          final elementData = message['data'] as Map<String, dynamic>;
          final elementType = elementData['element_type'] as String? ?? 'text';
          print('Processing opening sequence element: $elementData');
          print('Element type: $elementType');

          // 检查是否为图片类型，如果是则跳过显示，直接请求下一条
          if (elementType == 'image') {
            print('Skipping image element, requesting next element');

            // 如果不是最后一条，自动请求下一条
            if (!(elementData['is_last'] ?? false)) {
              // 发送 action:next 来获取下一条演绎内容
              _chatChannel?.sink("", targetAgentId: "action:next");
            } else {
              // 如果是最后一条图片，直接结束演绎序列
              add(_MessageReceived({
                'type': 'opening_sequence_complete_internal',
              }));
            }
            break; // 跳过后续的消息创建逻辑
          }

          // *** 优雅的解决方案在这里 ***
          // 处理选项 (choice) 元素
          if (elementType == 'choice') {
            print('Processing choice element, switching to gameplay mode');

            final choices = (elementData['choices'] as List<dynamic>? ?? [])
                .map<Choice>((c) => Choice.fromJson(c as Map<String, dynamic>))
                .toList();

            print('Parsed choices: ${choices.length} choices found');
            for (int i = 0; i < choices.length; i++) {
              print('  Choice $i: ${choices[i].text}');
            }

            final choicePromptMessage = Message(
              id: 'opening_choice_prompt_${DateTime.now().millisecondsSinceEpoch}',
              chatId: state.chatId ?? '',
              content: elementData['content'] as String? ?? elementData['content_or_prompt'] as String? ?? '你决定怎么做？',
              role: MessageRole.narration,
              createdAt: DateTime.now(),
              metadata: const {'isHistory': true},
            );

            final updatedMessages = List<Message>.from(state.messages)
              ..add(choicePromptMessage);

            // *** 在这里调用 emit 是完全安全的！***
            emit(state.copyWith(
              messages: updatedMessages,
              currentChoices: choices,
              // 【修复】切换到游戏模式以显示选项
              storyInteractionState: StoryInteractionState.gameplay,
              // 【修复】结束电影式的"点击继续"模式
              isProgressiveStoryMode: false,
            ));
            break; // 处理完成
          }

          // 处理旁白和对话 (narration & dialogue)
          // 根据 element_type 和 agent_index/character_id 来判断消息角色
          MessageRole messageRole;
          String? agentId;

          // 优先使用agent_index，然后回退到character_id
          final agentIndex = elementData['agent_index'] as int?;
          if (agentIndex != null && agentIndex < state.participants.length) {
            agentId = state.participants[agentIndex].id; // 将序号转换为UUID
            print('Agent resolved: index $agentIndex -> agent ${state.participants[agentIndex].name} ($agentId)');
          } else {
            agentId = elementData['character_id']; // 回退到旧的character_id
            if (agentIndex != null) {
              print('Warning: agent_index $agentIndex out of range, participants count: ${state.participants.length}');
            }
          }

          if (elementType == 'dialogue') {
            // 在故事模式下，需要区分女主角和男主角的对话
            if (state.storyDetail != null && state.protagonistAgent != null) {
              print('=== PROTAGONIST DEBUG ===');
              print('Current agentId: $agentId');
              print('Protagonist agent ID: ${state.protagonistAgent!.id}');
              print('Protagonist agent name: ${state.protagonistAgent!.name}');
              print('Is protagonist speaking: ${agentId == state.protagonistAgent!.id}');
              print('========================');

              // 如果character_id是女主角的ID，则显示为用户消息（右侧）
              if (agentId == state.protagonistAgent!.id) {
                messageRole = MessageRole.user;
                print('Setting message as USER (right side)');
              } else {
                // 否则显示为助手消息（左侧）
                messageRole = MessageRole.assistant;
                print('Setting message as ASSISTANT (left side)');
              }
            } else {
              print('=== PROTAGONIST DEBUG ===');
              print('Story detail: ${state.storyDetail != null}');
              print('Protagonist agent: ${state.protagonistAgent != null}');
              if (state.protagonistAgent != null) {
                print('Protagonist agent ID: ${state.protagonistAgent!.id}');
                print('Protagonist agent name: ${state.protagonistAgent!.name}');
              }
              print('========================');
              // 非故事模式，dialogue 类型总是显示为对话气泡
              messageRole = MessageRole.assistant;
            }
          } else if (agentId != null) {
            // 有 character_id 的其他类型也显示为对话气泡
            messageRole = MessageRole.assistant;
          } else {
            // 其他情况显示为旁白
            messageRole = MessageRole.narration;
          }

          final openingMessage = Message(
            id: 'opening_${elementData['sequence_index']}_${DateTime.now().millisecondsSinceEpoch}',
            chatId: state.chatId ?? '',
            content: elementData['content'] ?? '',
            role: messageRole,
            agentId: agentId,
            createdAt: DateTime.now(),
            // 修复：为开场演绎消息添加标记，避免被过滤器误删
            metadata: {'is_opening_sequence': true},
          );

          final updatedMessages = List<Message>.from(state.messages)
            ..add(openingMessage);

          emit(state.copyWith(
            messages: updatedMessages,
            // 关键修复：收到旁白或对话后，进入cinematic模式，等待用户点击继续
            storyInteractionState: (elementData['is_last'] ?? false)
                ? StoryInteractionState.gameplay
                : StoryInteractionState.cinematic,
            isProgressiveStoryMode: !(elementData['is_last'] ?? false),
            isWaitingForNextMessage: false, // 重置防连点保护，允许下一次点击
            interactionState: PlayerInteractionState.playing, // 重置为可交互状态
            isReplying: false, // 重置回复状态
          ));
          break;

        case 'opening_sequence_complete':
          // PRD要求：开场演绎完成后，切换到游戏模式
          print('Opening sequence completed, switching to gameplay mode');
          emit(state.copyWith(
            isProgressiveStoryMode: false,
            storyInteractionState: StoryInteractionState.gameplay,
          ));
          break;

        case 'message_chunk':
          final messages = List<Message>.from(state.messages);
          final chunk = message['content_chunk']?.toString() ?? '';
          final tempId = message['temp_id']?.toString();

          if (chunk.isNotEmpty && tempId != null) {
            // 查找是否已存在具有此 tempId 的消息
            int existingMessageIndex = messages.indexWhere((m) => m.id == tempId);

            if (existingMessageIndex != -1) {
              // 如果已存在，追加内容
              final existingMessage = messages[existingMessageIndex];
              messages[existingMessageIndex] = existingMessage.copyWith(
                content: existingMessage.content + chunk,
              );
            } else {
              // 如果不存在，创建一条新的临时消息
              final newAiMessage = Message(
                id: tempId, // 使用 temp_id 作为临时ID
                chatId: state.chatId ?? '',
                content: chunk,
                role: MessageRole.assistant,
                agentId: message['agent_id']?.toString(),
                createdAt: DateTime.now(),
                // 标记为非历史消息，以显示打字机效果
                metadata: {'isHistory': false},
              );
              messages.add(newAiMessage);
            }
            // 关键修复：在流式传输期间，保持 isReplying 为 true
            emit(state.copyWith(messages: messages));
          }
          break;

        case 'message_final':
          final messages = List<Message>.from(state.messages);
          final tempId = message['temp_id']?.toString();
          final finalMessageData = message['final_message'] as Map<String, dynamic>?;

          if (tempId != null && finalMessageData != null) {
            final finalMessage = Message.fromJson(finalMessageData);
            int tempMessageIndex = messages.indexWhere((m) => m.id == tempId);

            if (tempMessageIndex != -1) {
              // 情况1: 找到了临时消息，替换它
              messages[tempMessageIndex] = finalMessage;
            } else {
              // --- 核心修复 ---
              // 情况2: 没找到临时消息 (回复太快，没有chunk)，直接添加新消息
              messages.add(finalMessage);
            }
          }

          // --- ▼▼▼ 新增逻辑开始 ▼▼▼ ---
          // 检查最终消息是否附带了新的用户选项
          List<Choice>? newChoices;
          if (message['choices'] != null && message['choices'] is List) {
            final rawChoices = message['choices'] as List;
            if (rawChoices.isNotEmpty) {
              // 正确解析choice对象，支持target_agent_index
              newChoices = rawChoices.map((c) {
                if (c is Map<String, dynamic>) {
                  return Choice.fromJson(c);
                } else {
                  return Choice.fromString(c.toString());
                }
              }).toList();
              print('Received ${newChoices.length} new user choices from AI.');
            }
          }
          // --- ▲▲▲ 新增逻辑结束 ▲▲▲ ---

          // 实现"先回复，后生成选项"的UI效果
          // 1. 先显示NPC回复完成，开始生成自动回复选项
          emit(state.copyWith(
            messages: messages,
            isReplying: false, // NPC回复完成
            isGeneratingChoices: newChoices != null && newChoices.isNotEmpty, // 只有当有选项时才显示生成中
            currentChoices: [], // 清空旧选项
          ));

          // 2. 如果有新选项，模拟生成过程
          if (newChoices != null && newChoices.isNotEmpty) {
            // 短暂延迟，让用户看到"自动回复生成中"
            await Future.delayed(const Duration(milliseconds: 800));

            // 3. 显示最终的选项
            emit(state.copyWith(
              isGeneratingChoices: false, // 结束选项生成
              currentChoices: newChoices,
            ));
          }
          break;

        case 'stream_end':
          final messages = List<Message>.from(state.messages);
          final tempId = message['temp_id']?.toString();
          final finalMessageData = message['final_message'] as Map<String, dynamic>?;

          if (tempId != null && finalMessageData != null) {
            final finalMessage = Message.fromJson(finalMessageData);
            int tempMessageIndex = messages.indexWhere((m) => m.id == tempId);

            if (tempMessageIndex != -1) {
              // 情况1: 找到了临时消息，替换它
              messages[tempMessageIndex] = finalMessage;
            } else {
              // 情况2: 没找到临时消息 (回复太快，没有chunk)，直接添加新消息
              messages.add(finalMessage);
            }
          }

          // 文本流结束，停止 "输入中..." 状态，并开始 "生成选项..." 状态
          emit(state.copyWith(
            messages: messages,
            isReplying: false, // AI回复完成
            isGeneratingChoices: true, // 开始显示"选项生成中"
            currentChoices: [], // 清空旧选项
          ));
          break;

        case 'choices_updated':
          List<Choice>? newChoices;
          if (message['choices'] != null && message['choices'] is List) {
            final rawChoices = message['choices'] as List;
            newChoices = rawChoices
                .map((c) => Choice.fromJson(c as Map<String, dynamic>))
                .toList();
          }

          // 选项已收到，更新UI
          emit(state.copyWith(
            isGeneratingChoices: false, // 停止"选项生成中"
            currentChoices: newChoices ?? [],
          ));
          break;

        case 'audio_update':
          // 处理音频更新
          if (message['audio_url'] != null) {
            final messages = List<Message>.from(state.messages);
            final audioUrl = message['audio_url'].toString();
            final messageText = message['text']?.toString() ?? '';
            
            // 查找对应的消息并更新音频URL
            for (int i = messages.length - 1; i >= 0; i--) {
              if (messages[i].role == MessageRole.assistant && 
                  messages[i].content == messageText &&
                  (messages[i].audioUrl == null || messages[i].audioUrl!.isEmpty)) {
                messages[i] = messages[i].copyWith(audioUrl: audioUrl);
                emit(state.copyWith(messages: messages, isReplying: false));
                return;
              }
            }
          }
          break;

        case 'game_state_update':
          // 织梦者引擎：处理游戏状态更新
          final descriptions = message['descriptions'] as List<dynamic>?;
          if (descriptions != null && descriptions.isNotEmpty) {
            // 显示状态变化通知
            add(_GameStateChanged(descriptions.cast<String>()));
          }
          break;

        case 'opening_element_received':
          // 处理内部开场演绎事件
          final elementData = message['data'] as Map<String, dynamic>;
          emit(state.copyWith(
            isProgressiveStoryMode: true,
            currentStoryElementIndex: elementData['sequence_index'] ?? 0,
          ));
          break;
        
        case 'opening_last_element_received':
          emit(state.copyWith(
            isProgressiveStoryMode: false,
            storyInteractionState: StoryInteractionState.gameplay,
          ));
          break;
        
        case 'opening_sequence_complete_internal':
          // 修复：保持现有的选项状态，只更新必要的字段
          emit(state.copyWith(
            isProgressiveStoryMode: false,
            storyInteractionState: StoryInteractionState.gameplay,
            // 注意：不要覆盖 currentChoices，保持现有的选项状态
          ));
          break;

        case 'choices_regenerated':
          // 处理重新生成的选项
          List<Choice>? newChoices;
          if (message['choices'] != null && message['choices'] is List) {
            final rawChoices = message['choices'] as List;
            if (rawChoices.isNotEmpty) {
              // 正确解析choice对象，避免显示target_agent_id
              newChoices = rawChoices.map((c) {
                if (c is Map<String, dynamic>) {
                  return Choice.fromJson(c);
                } else {
                  return Choice.fromString(c.toString());
                }
              }).toList();
              print('Received ${newChoices.length} regenerated choices.');
            }
          }

          // 模拟生成过程，让用户看到加载效果
          if (newChoices != null && newChoices.isNotEmpty) {
            await Future.delayed(const Duration(milliseconds: 500));
          }

          emit(state.copyWith(
            isGeneratingChoices: false,
            currentChoices: newChoices ?? [],
            areChoicesCollapsed: false, // 展开显示新选项
          ));
          break;

        case 'choices_regeneration_failed':
          // 处理重新生成失败
          print('Failed to regenerate choices: ${message['error']}');
          emit(state.copyWith(
            isGeneratingChoices: false,
            // 保持原有选项不变
          ));
          break;

        default:
          // 处理其他消息类型或完整的 Message 对象
          if (message['choices'] != null && !state.justMadeChoice) {
            final rawChoices = message['choices'] as List;
            final choices = rawChoices.map<Choice>((c) {
              if (c is String) return Choice(text: c);
              if (c is Map) return Choice.fromJson(Map<String, dynamic>.from(c));
              return Choice(text: c.toString());
            }).toList();
            emit(state.copyWith(
              currentChoices: choices,
              justMadeChoice: false, // 重置标志
            ));
          }

          // 处理完整消息
          if (message['text'] != null && message['text'].toString().isNotEmpty) {
            final messages = List<Message>.from(state.messages);
            final newMessage = Message(
              id: message['id']?.toString() ?? DateTime.now().millisecondsSinceEpoch.toString(),
              chatId: state.chatId ?? '',
              content: message['text'].toString(),
              role: message['role'] == 'user' ? MessageRole.user : MessageRole.assistant,
              agentId: message['agent_id']?.toString(),
              audioUrl: message['audio_url']?.toString(),
              createdAt: DateTime.tryParse(message['created_at']?.toString() ?? '') ?? DateTime.now(),
              // 修复：完整消息应该立即标记为历史消息，停止打字机效果
              metadata: {'isHistory': true},
            );
            messages.add(newMessage);

            final choices = message['choices'] != null && !state.justMadeChoice
                ? (message['choices'] as List).map<Choice>((c) {
                    if (c is String) return Choice(text: c);
                    if (c is Map) return Choice.fromJson(Map<String, dynamic>.from(c));
                    return Choice(text: c.toString());
                  }).toList()
                : null;
            emit(state.copyWith(
              messages: messages,
              isReplying: false,
              currentChoices: choices,
              justMadeChoice: false, // 重置标志
            ));
          }
          break;
      }
    }
  }

  void _onConnectionClosed(_ConnectionClosed event, Emitter<ChatPlayerState> emit,) {
    print("Chat connection closed.");
  }

  void _onSwitchTargetAgent(SwitchTargetAgent event, Emitter<ChatPlayerState> emit,) {
    emit(state.copyWith(currentTargetAgent: event.agent));
  }

  void _onStartChapterIntro(StartChapterIntro event, Emitter<ChatPlayerState> emit) {
    print('StartChapterIntro: Setting showChapterIntro to true with ${event.chapterElements.length} elements');
    emit(state.copyWith(
      showChapterIntro: true,
      chapterElements: event.chapterElements,
    ));
  }

  void _onDismissChapterIntro(DismissChapterIntro event, Emitter<ChatPlayerState> emit) async {
    // 关闭章节介绍弹窗
    emit(state.copyWith(showChapterIntro: false));
    
    print('=== DismissChapterIntro Debug ===');
    print('Story Detail: ${state.storyDetail != null}');
    print('Has Chat Channel: ${_chatChannel != null}');
    
    // 如果是故事模式，发送"next"动作来触发opening_sequence
    if (state.storyDetail != null && _chatChannel != null) {
      print('Sending "next" action to trigger opening_sequence');
      // 发送next动作，这会触发后端发送opening_sequence
      // 注意：我们需要发送一个特殊的动作消息，而不是普通的聊天消息
      _chatChannel!.sink("", targetAgentId: "action:next"); // 使用特殊的targetAgentId来标识这是一个动作
      
      // 设置为渐进式故事模式，等待WebSocket消息
      emit(state.copyWith(
        isProgressiveStoryMode: true,
        currentStoryElementIndex: -1, // 还没有显示任何元素
      ));
    } else {
      // 非故事模式的原有逻辑
      if (state.chapterElements != null && state.chapterElements!.isNotEmpty) {
        // 自动显示前2条消息
        final messages = List<Message>.from(state.messages);
        
        for (int i = 0; i < 2 && i < state.chapterElements!.length; i++) {
          final element = state.chapterElements![i];
          final message = _createMessageFromElement(element, i);
          messages.add(message);
          
          // 添加延迟以模拟逐条显示
          if (i > 0) {
            await Future.delayed(const Duration(milliseconds: 800));
          }
          
          emit(state.copyWith(
            messages: messages,
            currentStoryElementIndex: i,
          ));
        }
        
        // 如果还有更多元素，设置为等待用户点击状态
        if (state.chapterElements!.length > 2) {
          emit(state.copyWith(
            isProgressiveStoryMode: true,
            currentStoryElementIndex: 1, // 已显示到索引1（第2条）
          ));
        } else {
          // 如果只有2条或更少，直接结束
          emit(state.copyWith(isProgressiveStoryMode: false));
        }
      }
    }
  }

  void _onShowNextStoryElement(ShowNextStoryElement event, Emitter<ChatPlayerState> emit) async {
    // 如果是故事模式，发送 "next" 动作到后端
    if (state.storyDetail != null && _chatChannel != null) {
      print('Sending "next" action for next story element');
      _chatChannel!.sink("", targetAgentId: "action:next");
      return;
    }

    // 非故事模式的原有逻辑（保持向后兼容）
    if (state.chapterElements == null || state.currentStoryElementIndex >= state.chapterElements!.length - 1) {
      return;
    }

    // 显示加载状态
    emit(state.copyWith(isLoadingNextElement: true));

    // 模拟0.5秒延迟
    await Future.delayed(const Duration(milliseconds: 500));

    final nextIndex = state.currentStoryElementIndex + 1;
    final element = state.chapterElements![nextIndex];

    // 将下一个元素转换为消息并添加到消息列表
    final messages = List<Message>.from(state.messages);
    final message = _createMessageFromElement(element, nextIndex);
    messages.add(message);

    // 如果已经显示完所有元素，结束渐进式故事模式
    if (nextIndex >= state.chapterElements!.length - 1) {
      emit(state.copyWith(
        messages: messages,
        isLoadingNextElement: false,
        isProgressiveStoryMode: false,
        currentStoryElementIndex: nextIndex,
      ));
    } else {
      emit(state.copyWith(
        messages: messages,
        isLoadingNextElement: false,
        currentStoryElementIndex: nextIndex,
      ));
    }
  }

  // 辅助方法：将章节元素转换为消息
  Message _createMessageFromElement(Map<String, dynamic> element, int index) {
    final content = element['content_or_prompt'] as String? ?? '';
    final characterId = element['character_id'] as String?;
    
    // 确定消息角色
    MessageRole role;
    String? agentId;
    
    if (characterId != null) {
      role = MessageRole.assistant;
      agentId = characterId;
    } else {
      role = MessageRole.narration;
    }
    
    return Message(
      id: 'chapter_element_${index}_${DateTime.now().millisecondsSinceEpoch}',
      chatId: state.chatId ?? '',
      content: content,
      role: role,
      agentId: agentId,
      createdAt: DateTime.now(),
    );
  }

  void _onCompleteProgressiveStory(CompleteProgressiveStory event, Emitter<ChatPlayerState> emit) {
    emit(state.copyWith(
      isProgressiveStoryMode: false,
      showChapterIntro: false,
    ));
  }

  @override
  Future<void> close() {
    _chatSubscription?.cancel();
    _chatChannel?.close();
    return super.close();
  }

  // --- 在文件下方，添加所有从 StoryPlayerBloc 复制过来的 `_on...` 方法的实现 ---
  void _onTapToContinue(TapToContinue event, Emitter<ChatPlayerState> emit) {
    if (state.storyDetail == null) return; // 确保只在故事模式下响应

    // PRD要求：如果已经在gameplay模式，说明开场演绎已完成，不需要特殊处理
    if (state.storyInteractionState == StoryInteractionState.gameplay) {
      print('TapToContinue: Already in gameplay mode, ignoring');
      return;
    }

    // 防连点保护：如果正在等待下一条消息，忽略此次点击
    if (state.isWaitingForNextMessage) {
      print('TapToContinue: Already waiting for next message, ignoring duplicate tap');
      return;
    }

    // PRD要求：设置为加载状态，提供用户反馈，同时启用防连点保护
    emit(state.copyWith(
      interactionState: PlayerInteractionState.loading,
      isWaitingForNextMessage: true, // 启用防连点保护
    ));

    // PRD要求：发送next动作到后端，触发开场演绎序列
    _chatChannel?.sink("", targetAgentId: "action:next");
    print('TapToContinue: Sent action:next to trigger opening sequence');
  }

  void _onMakeChoice(MakeChoice event, Emitter<ChatPlayerState> emit) {
      // 逻辑完全从 StoryPlayerBloc 迁移过来
      if (state.storyDetail == null) return;

      final userChoiceNode = story_models.StoryNode(
        id: 'user_choice_${DateTime.now().millisecondsSinceEpoch}',
        type: story_models.StoryNodeType.dialogue,
        content: event.choiceText,
        characterName: 'user_is_narrator', // 特殊标记
      );

      emit(state.copyWith(
        displayedNodes: [...state.displayedNodes, userChoiceNode],
        currentNode: userChoiceNode,
        interactionState: PlayerInteractionState.loading,
      ));

      _chatChannel?.sink(event.choiceText);
  }

  void _onContinueAfterChapter(ContinueAfterChapter event, Emitter<ChatPlayerState> emit) {
    if (state.storyDetail == null) return;

    // PRD要求：章节完成后，切换回gameplay状态继续游戏
    emit(state.copyWith(
      storyInteractionState: StoryInteractionState.gameplay,
      interactionState: PlayerInteractionState.playing,
    ));
    
    print('ContinueAfterChapter: Switched back to gameplay mode');
  }

  void _onContinueCurrentChapter(ContinueCurrentChapter event, Emitter<ChatPlayerState> emit) {
    // 从章节完成状态返回到游戏状态，继续当前章节
    emit(state.copyWith(
      storyInteractionState: StoryInteractionState.gameplay,
      interactionState: PlayerInteractionState.playing,
    ));

    print('ContinueCurrentChapter: Switched back to gameplay mode');
  }

  void _onLoadNextChapter(LoadNextChapter event, Emitter<ChatPlayerState> emit) async {
    // 确保我们有当前chatId和userId
    final currentChatId = state.chatId;
    final userId = state.userId;

    if (currentChatId == null || userId == null) {
      print('LoadNextChapter: Missing chatId or userId');
      return;
    }

    // 显示加载状态
    emit(state.copyWith(status: ChatPlayerStatus.loading));

    try {
      // 调用 repository 的新方法
      final newChatId = await _chatRepository.startNextChapter(currentChatId, userId);

      if (newChatId != null) {
        // 派发内部事件，让UI层处理导航
        add(_NextChapterLoaded(newChatId));
      } else {
        // 如果 newChatId 为 null, 说明已经是最后一章
        emit(state.copyWith(
          status: ChatPlayerStatus.success,
          errorMessage: '已经是最后一章了！恭喜通关！'
        ));
      }
    } catch (e) {
      // 处理错误
      print('LoadNextChapter Error: $e');
      emit(state.copyWith(
        status: ChatPlayerStatus.failure,
        errorMessage: e.toString().replaceAll('Exception: ', '')
      ));
    }
  }

  void _onNextChapterLoaded(_NextChapterLoaded event, Emitter<ChatPlayerState> emit) {
    // 这个事件主要是为了让UI层能够监听到新的chatId并进行导航
    // 我们在state中设置一个特殊的状态来表示需要导航
    emit(state.copyWith(
      status: ChatPlayerStatus.success,
      errorMessage: 'NAVIGATE_TO_CHAT:${event.newChatId}' // 特殊的错误消息格式用于导航
    ));
  }

  void _onNodeReceived(_NodeReceived event, Emitter<ChatPlayerState> emit) {
    if (state.storyDetail == null) return;

    story_models.StoryNode finalNode = event.node;

    if (finalNode.characterName != null &&
        finalNode.characterName!.startsWith('character_') &&
        state.storyDetail != null) {
      try {
        final characterId = finalNode.characterName!.replaceFirst('character_', '');
        final agent = state.storyDetail!.agents.firstWhere(
          (a) => a.id == characterId,
          orElse: () {
            return Agent(id: '', name: finalNode.characterName!, description: '', tags: [], isPublic: false, viewCount: 0, dialogueCount: 0, createdAt: '', updatedAt: '', author: '', isSystemAgent: true);
          }
        );

        finalNode = finalNode.copyWith(
          characterAvatarUrl: agent.avatarUrl,
          characterName: agent.name
        );

      } catch (e) {
        print("[ChatPlayerBloc] _onNodeReceived: Failed to find agent for node. Error: $e");
      }
    }

    emit(state.copyWith(
      displayedNodes: [...state.displayedNodes, finalNode],
      currentNode: finalNode,
      interactionState: PlayerInteractionState.playing,
    ));
  }

  void _onScoreUpdated(_ScoreUpdated event, Emitter<ChatPlayerState> emit) {
    if (state.storyDetail == null) return;

    // PRD要求：更新任务进度并触发分数动画
    emit(state.copyWith(
      currentProgress: event.newTotal,
      // --- ▼▼▼ 核心修正 ▼▼▼ ---
      // 只有当章节确定完成时，才将状态切换为 finished。
      // 在其他情况下，保持当前的状态（例如，继续保持 cinematic 模式），而不是强制切换到 gameplay。
      storyInteractionState: event.isChapterComplete
        ? StoryInteractionState.finished
        : state.storyInteractionState, // <-- 使用 state.storyInteractionState 维持原状
      // --- ▲▲▲ 核心修正 ▲▲▲ ---
    ));

    // 注意：分数动画在StoryInteractionView的BlocListener中触发
    print('Score updated: +${event.increment} -> ${event.newTotal}');
  }

  void _onLoadingReceived(_LoadingReceived event, Emitter<ChatPlayerState> emit) {
    if (state.storyDetail == null) return;
    emit(state.copyWith(interactionState: PlayerInteractionState.loading));
  }

  void _onChapterCompleted(_ChapterCompleted event, Emitter<ChatPlayerState> emit) {
    if (state.storyDetail == null) return;
    emit(state.copyWith(interactionState: PlayerInteractionState.finished));
  }

  void _onEnterChatScene(_EnterChatScene event, Emitter<ChatPlayerState> emit) {
    if (state.storyDetail == null) return;
    emit(state.copyWith(
        isChatSceneReady: true,
        agentsInScene: event.agents,
        currentTargetAgent: event.agents.isNotEmpty ? event.agents.first : null,
    ));
  }

  void _onGameStateChanged(_GameStateChanged event, Emitter<ChatPlayerState> emit) {
    // 织梦者引擎：处理游戏状态变化
    // 这里可以触发状态变化通知的显示
    // 具体的UI通知将在StoryInteractionView中通过BlocListener处理
    print('Game state changed: ${event.descriptions.join(', ')}');
  }

  void _onGameStateSyncReceived(_GameStateSyncReceived event, Emitter<ChatPlayerState> emit) {
    print('Processing game state sync event');

    if (event.hasHistoryMessages) {
      // 修复：在剧情演绎过程中，需要保留当前正在演绎的消息
      List<Message> finalMessages;

      if (state.isProgressiveStoryMode) {
        // 如果正在进行剧情演绎，需要合并历史消息和当前演绎消息
        print('In progressive story mode, merging messages');

        // 找出当前状态中的演绎消息（带有is_opening_sequence标记的）
        final currentOpeningMessages = state.messages.where(
          (msg) => msg.metadata?['is_opening_sequence'] == true
        ).toList();

        // 合并：历史消息 + 当前演绎消息
        finalMessages = List<Message>.from(event.messages);
        for (final openingMsg in currentOpeningMessages) {
          // 避免重复添加相同的消息
          if (!finalMessages.any((msg) => msg.id == openingMsg.id)) {
            finalMessages.add(openingMsg);
          }
        }

        print('Merged ${event.messages.length} history + ${currentOpeningMessages.length} opening = ${finalMessages.length} total messages');
      } else {
        // 非演绎模式，直接使用同步的消息
        finalMessages = event.messages;
      }

      // 1. 更新消息列表、参与者和主角信息
      emit(state.copyWith(
        messages: finalMessages,
        participants: event.participants,
        protagonistAgent: event.protagonistAgent,
      ));

      // 2. 如果是故事模式，则额外更新故事相关的状态
      if (state.storyDetail != null && !state.isProgressiveStoryMode) {
        emit(state.copyWith(
          storyInteractionState: StoryInteractionState.gameplay, // 直接进入游戏模式
          showChapterIntro: false, // 阻止章节介绍弹窗
        ));
      }
    } else {
      // 即使没有历史消息，也要更新参与者和主角信息
      emit(state.copyWith(
        participants: event.participants,
        protagonistAgent: event.protagonistAgent,
      ));
    }

    print('Game state sync completed. Protagonist: ${event.protagonistAgent?.name}');
  }

  Future<void> _onLoadMoreMessages(
    LoadMoreMessages event,
    Emitter<ChatPlayerState> emit,
  ) async {
    if (state.chatId == null || !state.hasMoreMessages) return;

    try {
      // 使用当前消息数量作为offset加载更多历史消息
      final moreMessages = await _chatRepository.getMessages(
        state.chatId!,
        limit: 20, // 每次加载20条
        offset: state.messages.length,
      );

      if (moreMessages.isNotEmpty) {
        // 为历史消息添加isHistory标记
        final historyMessages = moreMessages.map((m) => 
          m.copyWith(metadata: {...(m.metadata ?? {}), 'isHistory': true})
        ).toList();

        // 将新加载的消息插入到现有消息列表的最前面
        final updatedMessages = [...historyMessages, ...state.messages];
        
        emit(state.copyWith(
          messages: updatedMessages,
          hasMoreMessages: moreMessages.length >= 20, // 如果返回的消息数少于请求数，说明没有更多了
        ));
      } else {
        // 没有更多消息了
        emit(state.copyWith(hasMoreMessages: false));
      }
    } catch (e) {
      print('Error loading more messages: $e');
      // 发生错误时也设置为没有更多消息，避免重复请求
      emit(state.copyWith(hasMoreMessages: false));
    }
  }

  // 新增：处理重新生成选项的逻辑
  void _onRegenerateChoicesRequested(
    RegenerateChoicesRequested event,
    Emitter<ChatPlayerState> emit,
  ) {
    print('DEBUG: RegenerateChoicesRequested triggered');

    // 1. 立即进入加载状态
    emit(state.copyWith(
      isGeneratingChoices: true,
      currentChoices: [], // 清空旧选项，确保显示加载指示器
      areChoicesCollapsed: false, // 展开选项框以显示加载状态
    ));

    // 2. 通过 WebSocket 发送重新生成指令
    print('DEBUG: Sending regenerate_choices action');
    _chatChannel?.sink(
      "", // content 为空
      action: "regenerate_choices",
    );
  }

  // 新增：处理展开/收缩的逻辑
  void _onToggleChoicesExpansion(
    ToggleChoicesExpansion event,
    Emitter<ChatPlayerState> emit,
  ) {
    emit(state.copyWith(areChoicesCollapsed: !state.areChoicesCollapsed));
  }

  // 修复：处理显示/隐藏选项面板的逻辑
  void _onToggleChoicesPanelVisibility(
    ToggleChoicesPanelVisibility event,
    Emitter<ChatPlayerState> emit,
  ) {
    // 核心修复：处理器只负责切换 showChoicesPanel 状态。
    // UI将根据 isGeneratingChoices 和 currentChoices 的状态，
    // 自动决定是显示加载指示器、显示选项，还是显示一个空面板。
    emit(state.copyWith(
      showChoicesPanel: !state.showChoicesPanel,
      areChoicesCollapsed: false, // 每次打开时都确保是展开状态
    ));
  }
}
