-- =================================================================
-- DANGER: THIS SCRIPT DELETES ALL YOUR DATA AND RESETS THE SCHEMA
-- =================================================================

-- Part 1: Clean up existing objects
-- -----------------------------------------------------------------

-- 1. Drop the trigger on the system table first to remove dependency
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- 2. Drop all public tables. CASCADE handles dependencies like foreign keys and indexes.
DROP TABLE IF EXISTS public.agent_memories CASCADE;
DROP TABLE IF EXISTS public.agent_world_infos CASCADE;
DROP TABLE IF EXISTS public.world_info_entries CASCADE;
DROP TABLE IF EXISTS public.world_infos CASCADE;
DROP TABLE IF EXISTS public.chat_summaries CASCADE;
DROP TABLE IF EXISTS public.messages CASCADE;
DROP TABLE IF EXISTS public.chat_participants CASCADE;
DROP TABLE IF EXISTS public.chats CASCADE;
DROP TABLE IF EXISTS public.story_agents CASCADE;
DROP TABLE IF EXISTS public.story_chapters CASCADE;
DROP TABLE IF EXISTS public.stories CASCADE;
DROP TABLE IF EXISTS public.agent_mode_configs CASCADE;
DROP TABLE IF EXISTS public.agents CASCADE;
DROP TABLE IF EXISTS public.user_profiles CASCADE;

-- 3. Drop all functions. Now they have no dependencies.
DROP FUNCTION IF EXISTS public.increment_view_count(UUID);
DROP FUNCTION IF EXISTS public.increment_dialogue_count(UUID);
DROP FUNCTION IF EXISTS public.create_user_profile_on_signup();
DROP FUNCTION IF EXISTS public.update_updated_at_column();
DROP FUNCTION IF EXISTS public.get_public_agents_with_creator(INT);
DROP FUNCTION IF EXISTS public.get_user_chat_list(UUID, INT);
DROP FUNCTION IF EXISTS public.health_check();
DROP FUNCTION IF EXISTS public.match_chat_memories(UUID, vector, INT, FLOAT);
DROP FUNCTION IF EXISTS public.get_active_world_info_entries(UUID, TEXT);
DROP FUNCTION IF EXISTS public.get_agent_with_mode_config(UUID, TEXT);
DROP FUNCTION IF EXISTS public.get_story_rankings(text);
DROP FUNCTION IF EXISTS public.get_agent_rankings(text);


-- Part 2: Recreate the entire schema
-- -----------------------------------------------------------------

-- 星恋AI V5.0 - 数据库初始化脚本 (统一消息流版)
-- 本脚本为重构后的全新设计，将所有互动统一为消息流模型。
-- 本脚本可安全重复执行。

-- ========================================
-- 1. 基础配置
-- ========================================
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
-- 启用向量扩展，为RAG记忆系统做准备
CREATE EXTENSION IF NOT EXISTS vector;

-- ========================================
-- 2. 核心表：用户与AI智能体 (模板)
-- ========================================

-- 用户档案表 (与 auth.users 关联)
CREATE TABLE IF NOT EXISTS public.user_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE NOT NULL,
    display_name VARCHAR(100),
    avatar_url TEXT,
    is_system_user BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
COMMENT ON TABLE public.user_profiles IS '存储用户的公开档案信息';

-- 智能体 (角色) 表 - 精炼版：消除冗余，明确职责
CREATE TABLE IF NOT EXISTS public.agents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,

    -- =================================================
    -- I. 核心规范化字段 (为性能和通用显示而缓存)
    -- =================================================
    name VARCHAR(100) NOT NULL,
    description TEXT,       -- 核心简介 (对应V2的data.description)
    personality TEXT,       -- 核心性格 (对应V2的data.personality)
    scenario TEXT,          -- 核心场景 (对应V2的data.scenario)
    first_mes TEXT,         -- 核心开场白 (统一字段，废弃opening_line)
    mes_example TEXT,       -- 核心对话示例 (对应V2的data.mes_example)

    -- =================================================
    -- II. 应用特定字段 (星恋AI的特色功能)
    -- =================================================
    image_url TEXT,         -- 主形象图/立绘 (高清，用于详情页)
    avatar_url TEXT,        -- 聊天头像 (小尺寸，用于聊天界面)
    voice_name VARCHAR(50) DEFAULT 'Kore' NOT NULL,
    tags JSONB DEFAULT '[]'::jsonb,
    is_public BOOLEAN DEFAULT true,
    gender VARCHAR(10),     -- 用于语音选择和称谓

    -- =================================================
    -- III. 高级提示词工程字段 (兼容TavernAI高级设定)
    -- =================================================
    creator_notes TEXT,     -- 创作者备注
    system_prompt TEXT,     -- 系统级提示词
    post_history_instructions TEXT, -- 历史后指令

    -- =================================================
    -- IV. 故事模式专用字段
    -- =================================================
    backstory_text TEXT,    -- 角色背景故事 (故事模式中的隐藏信息)
    image_generation_prompt TEXT, -- 角色形象图生成的prompt (用于追溯和优化)

    -- =================================================
    -- V. 原始数据与元数据 (未来兼容性的核心)
    -- =================================================
    "data" JSONB,           -- 存储完整的原始角色卡JSON，作为"单一事实来源"
    spec VARCHAR(20) DEFAULT 'chara_card_v2' NOT NULL, -- 原始卡片格式
    spec_version VARCHAR(10) DEFAULT '2.0' NOT NULL,   -- 原始卡片版本

    -- =================================================
    -- VI. 统计与系统字段
    -- =================================================
    view_count INTEGER DEFAULT 0 NOT NULL,
    dialogue_count INTEGER DEFAULT 0 NOT NULL,
    popularity INTEGER DEFAULT 0 NOT NULL,
    interaction_mode VARCHAR(50) DEFAULT 'roleplay' NOT NULL,
    is_system_agent BOOLEAN DEFAULT false NOT NULL,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
COMMENT ON TABLE public.agents IS '存储所有可交互的AI角色，是互动故事和聊天的基础';
COMMENT ON COLUMN public.agents.backstory_text IS '角色的个人背景故事，用于故事中可供玩家探索的隐藏信息';

-- 角色模式特定设定表 (解决场景设定冲突)
CREATE TABLE IF NOT EXISTS public.agent_mode_configs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agent_id UUID REFERENCES public.agents(id) ON DELETE CASCADE NOT NULL,
    mode VARCHAR(20) NOT NULL CHECK (mode IN ('chat', 'story')),

    -- 模式特定的开场白和场景设定
    mode_specific_first_mes TEXT,    -- 该模式下的专用开场白
    mode_specific_scenario TEXT,     -- 该模式下的专用场景设定
    mode_specific_instructions TEXT, -- 该模式下的专用指令

    -- 是否启用某些功能
    enable_mes_example BOOLEAN DEFAULT true,  -- 是否在该模式下使用对话示例
    enable_backstory BOOLEAN DEFAULT false,   -- 是否在该模式下透露背景故事

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(agent_id, mode)
);
COMMENT ON TABLE public.agent_mode_configs IS '存储角色在不同模式下的特定配置，解决聊天和故事模式的设定冲突';
COMMENT ON COLUMN public.agent_mode_configs.mode_specific_first_mes IS '该模式下的专用开场白，优先级高于agents.first_mes';
COMMENT ON COLUMN public.agent_mode_configs.enable_mes_example IS '故事模式下通常设为false，避免对话示例干扰剧情';


-- ========================================
-- 3. 故事模板核心表
-- ========================================

-- 故事表
CREATE TABLE IF NOT EXISTS public.stories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    title VARCHAR(200) NOT NULL,
    theme_prompt TEXT NOT NULL,
    worldview_text TEXT, -- 新增: 存储宏大世界观设定
    cover_image_url TEXT,
    cover_image_prompt TEXT, -- 新增: 存储故事封面图片的生成提示词

    -- ▼▼▼【织梦者引擎核心新增】▼▼▼
    source_analysis JSONB, -- 用于存储AI分析出的完整GDD，作为游戏运行时的"故事圣经"
    protagonist_agent_id UUID REFERENCES public.agents(id) ON DELETE SET NULL, -- 关联女主角的Agent ID
    -- ▲▲▲【织梦者引擎核心新增】▲▲▲

    tags JSONB DEFAULT '[]'::jsonb, -- 故事标签
    total_chapters INTEGER DEFAULT 0 NOT NULL, -- 章节总数

    popularity INTEGER DEFAULT 0 NOT NULL,
    is_public BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
COMMENT ON TABLE public.stories IS '互动故事的顶层定义';
COMMENT ON COLUMN public.stories.worldview_text IS '存储详细的世界观设定，如历史背景、势力划分、关键规则等';
COMMENT ON COLUMN public.stories.cover_image_prompt IS '存储故事封面图片的AI生成提示词，用于追溯和优化封面图片生成';
COMMENT ON COLUMN public.stories.source_analysis IS '存储AI对源小说的完整分析结果(GDD)，作为动态剧情生成的依据。';
COMMENT ON COLUMN public.stories.protagonist_agent_id IS '指定该故事中玩家扮演的女主角所对应的Agent ID。';
COMMENT ON COLUMN public.stories.tags IS '故事标签，以JSONB数组格式存储，用于分类和搜索';
COMMENT ON COLUMN public.stories.total_chapters IS '故事的章节总数，用于显示进度和完整性';

-- 故事章节表
CREATE TABLE IF NOT EXISTS public.story_chapters (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    story_id UUID REFERENCES public.stories(id) ON DELETE CASCADE NOT NULL,
    chapter_number INTEGER NOT NULL,
    title VARCHAR(200) NOT NULL,
    mission_objective_text TEXT, -- 本章的核心任务目标描述
    background_text TEXT,        -- 本章开局的背景介绍
    background_image_url TEXT,
    clear_condition_text TEXT,   -- 本章的通关条件描述
    opening_sequence JSONB,      -- 存储本章开头的演绎消息序列
    chapter_event_summary TEXT,  -- 供AI阅读的本章关键事件和情境摘要
    completion_summary_text TEXT, -- 章节完成后显示给玩家的总结文本，以女主角第一人称视角撰写
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(story_id, chapter_number)
);
COMMENT ON TABLE public.story_chapters IS '故事的章节列表和任务目标';
COMMENT ON COLUMN public.story_chapters.chapter_event_summary IS '由编译器预生成的、供AI在运行时阅读的本章关键事件、场景和情感基调的摘要。';

-- 故事与角色的关联表
CREATE TABLE IF NOT EXISTS public.story_agents (
    story_id UUID REFERENCES public.stories(id) ON DELETE CASCADE NOT NULL,
    agent_id UUID REFERENCES public.agents(id) ON DELETE CASCADE NOT NULL,
    PRIMARY KEY (story_id, agent_id)
);
COMMENT ON TABLE public.story_agents IS '存储故事与其包含的角色的多对多关系';

-- ========================================
-- 4. 统一对话核心表 (重构核心)
-- ========================================

-- 对话会话表
CREATE TABLE IF NOT EXISTS public.chats (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    story_id UUID REFERENCES public.stories(id) ON DELETE SET NULL, -- 如果是故事，则关联故事模板
    task_progress JSONB DEFAULT '{}'::jsonb, -- 存储游戏状态，格式见下方详细说明

    -- ▼▼▼【织梦者引擎核心新增】▼▼▼
    game_state JSONB, -- 用于存储当前游戏中的所有变量值，如好感度、误解度等
    -- ▲▲▲【织梦者引擎核心新增】▲▲▲

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
COMMENT ON TABLE public.chats IS '统一的对话会话表，包含普通聊天和互动故事';
COMMENT ON COLUMN public.chats.task_progress IS '存储游戏状态，JSON格式：
{
  "current_chapter_id": "chapter-uuid-1",
  "chapters": {
    "chapter-uuid-1": {
      "progress": 75,
      "status": "in_progress"
    },
    "chapter-uuid-2": {
      "progress": 0,
      "status": "not_started"
    }
  },
  "opening_sequence_index": -1
}';
COMMENT ON COLUMN public.chats.game_state IS '存储当前游戏会话的动态变量，例如：{"何以琛.好感度": 55, "何以琛.误解程度": 60}';

-- 会话参与者表 (AI角色)
CREATE TABLE IF NOT EXISTS public.chat_participants (
    chat_id UUID REFERENCES public.chats(id) ON DELETE CASCADE NOT NULL,
    agent_id UUID REFERENCES public.agents(id) ON DELETE CASCADE NOT NULL,
    PRIMARY KEY (chat_id, agent_id)
);
COMMENT ON TABLE public.chat_participants IS '会话参与者（AI角色）';

-- 统一的消息日志表 - 添加向量字段用于RAG记忆
CREATE TABLE IF NOT EXISTS public.messages (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    chat_id UUID REFERENCES public.chats(id) ON DELETE CASCADE NOT NULL,
    agent_id UUID REFERENCES public.agents(id) ON DELETE SET NULL, -- 说话的AI角色，旁白或用户时为NULL
    role VARCHAR(20) NOT NULL CHECK (role IN (
        'user',         -- 用户的输入
        'assistant',    -- AI角色的回复
        'narration',    -- 旁白或剧情描述
        'choice',       -- 提供给用户的选项
        'image'         -- 需要展示的图片
    )),
    content TEXT NOT NULL, -- 消息文本, 图片的prompt, 选项的引导语等
    metadata JSONB,      -- 用于存储额外信息, 如: {"choices": ["选项A", "选项B"]}, {"image_url": "..."}
    audio_url TEXT,      -- 保留语音URL字段
    embedding vector(768), -- 消息内容的向量表示，用于RAG检索 (使用768维度的Gemini embedding模型)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
COMMENT ON TABLE public.messages IS '统一的消息日志表，记录所有对话内容和剧情节点';

-- 对话摘要表 (长期记忆) - 增强版：支持模式感知
CREATE TABLE IF NOT EXISTS public.chat_summaries (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    chat_id UUID REFERENCES public.chats(id) ON DELETE CASCADE UNIQUE NOT NULL,
    summary_text TEXT,
    story_progress_summary TEXT, -- 故事模式专用：任务进度和剧情发展摘要
    relationship_summary TEXT,   -- 聊天模式专用：情感关系发展摘要
    last_summarized_message_id BIGINT,
    summary_type VARCHAR(20) DEFAULT 'chat' CHECK (summary_type IN ('chat', 'story')),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
COMMENT ON TABLE public.chat_summaries IS '存储对话的滚动摘要，支持聊天和故事两种模式的不同摘要策略';
COMMENT ON COLUMN public.chat_summaries.story_progress_summary IS '故事模式下的任务进度和剧情发展摘要';
COMMENT ON COLUMN public.chat_summaries.relationship_summary IS '聊天模式下的角色关系和情感发展摘要';

-- 世界书表 (知识库)
CREATE TABLE IF NOT EXISTS public.world_infos (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_public BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
COMMENT ON TABLE public.world_infos IS '世界书/知识库的顶层容器';

-- 世界书条目表
CREATE TABLE IF NOT EXISTS public.world_info_entries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    world_info_id UUID REFERENCES public.world_infos(id) ON DELETE CASCADE NOT NULL,
    keywords TEXT[] NOT NULL, -- 触发关键词数组
    content TEXT NOT NULL,
    priority INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
COMMENT ON TABLE public.world_info_entries IS '世界书的具体条目，包含关键词和内容';

-- 智能体与世界书的关联表
CREATE TABLE IF NOT EXISTS public.agent_world_infos (
    agent_id UUID REFERENCES public.agents(id) ON DELETE CASCADE NOT NULL,
    world_info_id UUID REFERENCES public.world_infos(id) ON DELETE CASCADE NOT NULL,
    PRIMARY KEY (agent_id, world_info_id)
);
COMMENT ON TABLE public.agent_world_infos IS '智能体与世界书的多对多关联';

-- 记忆表
CREATE TABLE IF NOT EXISTS public.agent_memories (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    agent_id UUID REFERENCES public.agents(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    memory_text TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(agent_id, user_id, memory_text) -- 防止重复记忆
);
COMMENT ON TABLE public.agent_memories IS '存储用户与智能体之间的专属记忆点';


-- ========================================
-- 5. 函数与触发器
-- ========================================

-- (健壮性) 先删除可能存在的旧函数
DROP FUNCTION IF EXISTS public.increment_view_count(UUID);
CREATE OR REPLACE FUNCTION public.increment_view_count(p_agent_id UUID)
RETURNS void AS $$
BEGIN
  UPDATE public.agents SET view_count = view_count + 1 WHERE id = p_agent_id;
END;
$$ LANGUAGE plpgsql;

DROP FUNCTION IF EXISTS public.increment_dialogue_count(UUID);
CREATE OR REPLACE FUNCTION public.increment_dialogue_count(p_agent_id UUID)
RETURNS void AS $$
BEGIN
  UPDATE public.agents SET dialogue_count = dialogue_count + 1 WHERE id = p_agent_id;
END;
$$ LANGUAGE plpgsql;

DROP FUNCTION IF EXISTS public.create_user_profile_on_signup();
CREATE OR REPLACE FUNCTION public.create_user_profile_on_signup()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.user_profiles (user_id, display_name, avatar_url)
  VALUES (new.id, new.raw_user_meta_data->>'display_name', new.raw_user_meta_data->>'avatar_url');
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 触发器不需要DROP，CREATE OR REPLACE会处理
CREATE OR REPLACE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.create_user_profile_on_signup();

DROP FUNCTION IF EXISTS public.update_updated_at_column();
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为需要自动更新时间的表绑定触发器
DROP TRIGGER IF EXISTS update_user_profiles_updated_at ON public.user_profiles;
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON public.user_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
DROP TRIGGER IF EXISTS update_agents_updated_at ON public.agents;
CREATE TRIGGER update_agents_updated_at BEFORE UPDATE ON public.agents FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
DROP TRIGGER IF EXISTS update_stories_updated_at ON public.stories;
CREATE TRIGGER update_stories_updated_at BEFORE UPDATE ON public.stories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
DROP TRIGGER IF EXISTS update_story_chapters_updated_at ON public.story_chapters;
CREATE TRIGGER update_story_chapters_updated_at BEFORE UPDATE ON public.story_chapters FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
-- 新增 chats 表的触发器
DROP TRIGGER IF EXISTS update_chats_updated_at ON public.chats;
CREATE TRIGGER update_chats_updated_at BEFORE UPDATE ON public.chats FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();


DROP FUNCTION IF EXISTS public.get_public_agents_with_creator(INT);
CREATE OR REPLACE FUNCTION public.get_public_agents_with_creator(p_limit int)
RETURNS TABLE (
    id uuid, user_id uuid, name character varying, description text, first_mes text, system_prompt text,
    image_url text, avatar_url text, gender character varying, is_public boolean, tags jsonb,
    interaction_mode character varying, voice_name character varying, is_system_agent boolean,
    view_count integer, dialogue_count integer, created_at timestamp with time zone, updated_at timestamp with time zone,
    creator_name character varying, creator_avatar_url text
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        a.id, a.user_id, a.name, a.description, a.first_mes, a.system_prompt,
        a.image_url, a.avatar_url, a.gender, a.is_public, a.tags,
        a.interaction_mode, a.voice_name, a.is_system_agent,
        a.view_count, a.dialogue_count, a.created_at, a.updated_at,
        COALESCE(up.display_name, '匿名创作者') AS creator_name,
        up.avatar_url AS creator_avatar_url
    FROM public.agents a
    LEFT JOIN public.user_profiles up ON a.user_id = up.user_id
    WHERE a.is_public = true
    ORDER BY a.dialogue_count DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;

-- (重构) get_user_chat_list 函数
DROP FUNCTION IF EXISTS public.get_user_chat_list(UUID, INT);
CREATE OR REPLACE FUNCTION public.get_user_chat_list(p_user_id UUID, p_limit INT)
RETURNS TABLE (
    chat_id UUID,
    story_id UUID,
    participants JSONB,
    latest_message TEXT,
    latest_message_time TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    is_story BOOLEAN,
    display_name TEXT,
    display_avatar TEXT
) AS $$
BEGIN
    RETURN QUERY
    WITH latest_messages AS (
        -- ▼▼▼【核心修复 v1.1】▼▼▼
        -- 移除了不必要的 JOIN 和 WHERE 子句，避免列名歧义
        SELECT
            m.chat_id,
            m.content,
            m.created_at,
            ROW_NUMBER() OVER(PARTITION BY m.chat_id ORDER BY m.created_at DESC) as rn
        FROM public.messages m
        -- ▲▲▲【核心修复 v1.1】▲▲▲
    ),
    chat_participants_agg AS (
        SELECT
            cp.chat_id,
            jsonb_agg(jsonb_build_object('id', a.id, 'name', a.name, 'avatar_url', a.avatar_url)) as participants_json,
            (array_agg(a.name))[1] as agent_name,
            (array_agg(a.avatar_url))[1] as agent_avatar
        FROM public.chat_participants cp
        JOIN public.agents a ON cp.agent_id = a.id
        GROUP BY cp.chat_id
    )
    SELECT
        c.id as chat_id,
        c.story_id,
        cpa.participants_json as participants,
        lm.content as latest_message,
        lm.created_at as latest_message_time,
        c.updated_at,
        (c.story_id IS NOT NULL) as is_story,
        -- ▼▼▼【核心修复 v1.2】▼▼▼
        -- 将 COALESCE 的结果显式转换为 TEXT 类型，以匹配函数返回类型
        COALESCE(s.title, cpa.agent_name)::TEXT as display_name,
        -- ▲▲▲【核心修复 v1.2】▲▲▲
        COALESCE(s.cover_image_url, cpa.agent_avatar) as display_avatar
    FROM public.chats c
    LEFT JOIN latest_messages lm ON c.id = lm.chat_id AND lm.rn = 1
    LEFT JOIN chat_participants_agg cpa ON c.id = cpa.chat_id
    LEFT JOIN public.stories s ON c.story_id = s.id
    WHERE c.user_id = p_user_id
    ORDER BY c.updated_at DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;

DROP FUNCTION IF EXISTS public.health_check();
CREATE OR REPLACE FUNCTION public.health_check()
RETURNS TEXT AS $$
BEGIN
  RETURN 'ok';
END;
$$ LANGUAGE plpgsql;

-- RAG记忆检索函数
DROP FUNCTION IF EXISTS public.match_chat_memories(UUID, vector, INT, FLOAT);
CREATE OR REPLACE FUNCTION public.match_chat_memories(
    p_chat_id UUID,
    query_embedding vector(768),
    match_count INT DEFAULT 5,
    match_threshold FLOAT DEFAULT 0.78
)
RETURNS TABLE (
    id BIGINT,
    content TEXT,
    role VARCHAR(20),
    agent_id UUID,
    created_at TIMESTAMP WITH TIME ZONE,
    similarity FLOAT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        m.id,
        m.content,
        m.role,
        m.agent_id,
        m.created_at,
        1 - (m.embedding <=> query_embedding) AS similarity
    FROM public.messages m
    WHERE m.chat_id = p_chat_id
      AND m.embedding IS NOT NULL
      AND 1 - (m.embedding <=> query_embedding) > match_threshold
    ORDER BY m.embedding <=> query_embedding
    LIMIT match_count;
END;
$$ LANGUAGE plpgsql;

-- 世界书条目检索函数 (支持关键词匹配)
DROP FUNCTION IF EXISTS public.get_active_world_info_entries(UUID, TEXT);
CREATE OR REPLACE FUNCTION public.get_active_world_info_entries(
    p_agent_id UUID,
    p_search_text TEXT
)
RETURNS TABLE (
    id UUID,
    content TEXT,
    keywords TEXT[],
    priority INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        wie.id,
        wie.content,
        wie.keywords,
        wie.priority
    FROM public.world_info_entries wie
    JOIN public.agent_world_infos awi ON wie.world_info_id = awi.world_info_id
    WHERE awi.agent_id = p_agent_id
      AND wie.is_active = true
      AND (
        -- 检查关键词是否在搜索文本中出现
        EXISTS (
          SELECT 1 FROM unnest(wie.keywords) AS keyword
          WHERE p_search_text ILIKE '%' || keyword || '%'
        )
      )
    ORDER BY wie.priority DESC, wie.created_at ASC;
END;
$$ LANGUAGE plpgsql;

-- 获取模式感知的角色配置
DROP FUNCTION IF EXISTS public.get_agent_with_mode_config(UUID, TEXT);
CREATE OR REPLACE FUNCTION public.get_agent_with_mode_config(
    p_agent_id UUID,
    p_mode TEXT DEFAULT 'chat'
)
RETURNS TABLE (
    -- 基础角色信息
    id UUID,
    name VARCHAR(100),
    description TEXT,
    personality TEXT,
    scenario TEXT,
    first_mes TEXT,
    mes_example TEXT,
    image_url TEXT,
    avatar_url TEXT,
    voice_name VARCHAR(50),
    backstory_text TEXT,

    -- 模式特定配置
    effective_first_mes TEXT,
    effective_scenario TEXT,
    effective_instructions TEXT,
    should_use_mes_example BOOLEAN,
    should_use_backstory BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        a.id,
        a.name,
        a.description,
        a.personality,
        a.scenario,
        a.first_mes,
        a.mes_example,
        a.image_url,
        a.avatar_url,
        a.voice_name,
        a.backstory_text,

        -- 优先使用模式特定配置，否则使用默认配置
        COALESCE(amc.mode_specific_first_mes, a.first_mes) as effective_first_mes,
        COALESCE(amc.mode_specific_scenario, a.scenario) as effective_scenario,
        amc.mode_specific_instructions as effective_instructions,
        COALESCE(amc.enable_mes_example, true) as should_use_mes_example,
        COALESCE(amc.enable_backstory, CASE WHEN p_mode = 'story' THEN true ELSE false END) as should_use_backstory

    FROM public.agents a
    LEFT JOIN public.agent_mode_configs amc ON a.id = amc.agent_id AND amc.mode = p_mode
    WHERE a.id = p_agent_id;
END;
$$ LANGUAGE plpgsql;


-- =================================================================
-- 6. 行级安全策略 (RLS)
-- =================================================================

-- 开启RLS
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.stories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.story_chapters ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.story_agents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chats ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chat_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agent_memories ENABLE ROW LEVEL SECURITY;
-- 新表RLS
ALTER TABLE public.chat_summaries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.world_infos ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.world_info_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agent_world_infos ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agent_mode_configs ENABLE ROW LEVEL SECURITY;

-- 定义策略
DROP POLICY IF EXISTS "Allow read access to everyone" ON public.user_profiles;
CREATE POLICY "Allow read access to everyone" ON public.user_profiles FOR SELECT USING (true);
DROP POLICY IF EXISTS "Allow individual update access" ON public.user_profiles;
CREATE POLICY "Allow individual update access" ON public.user_profiles FOR UPDATE USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Allow read access for public and own agents" ON public.agents;
CREATE POLICY "Allow read access for public and own agents" ON public.agents FOR SELECT USING ((is_public = true) OR (auth.uid() = user_id));
DROP POLICY IF EXISTS "Allow individual insert access" ON public.agents;
CREATE POLICY "Allow individual insert access" ON public.agents FOR INSERT WITH CHECK (auth.uid() = user_id);
DROP POLICY IF EXISTS "Allow individual update access" ON public.agents;
CREATE POLICY "Allow individual update access" ON public.agents FOR UPDATE USING (auth.uid() = user_id);
DROP POLICY IF EXISTS "Allow individual delete access" ON public.agents;
CREATE POLICY "Allow individual delete access" ON public.agents FOR DELETE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Allow read access for public and own stories" ON public.stories;
CREATE POLICY "Allow read access for public and own stories" ON public.stories FOR SELECT USING ((is_public = true) OR (auth.uid() = user_id));
DROP POLICY IF EXISTS "Allow individual insert access" ON public.stories;
CREATE POLICY "Allow individual insert access" ON public.stories FOR INSERT WITH CHECK (auth.uid() = user_id);
DROP POLICY IF EXISTS "Allow individual update access" ON public.stories;
CREATE POLICY "Allow individual update access" ON public.stories FOR UPDATE USING (auth.uid() = user_id);
DROP POLICY IF EXISTS "Allow individual delete access" ON public.stories;
CREATE POLICY "Allow individual delete access" ON public.stories FOR DELETE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Allow read access to all authenticated users" ON public.story_chapters;
CREATE POLICY "Allow read access to all authenticated users" ON public.story_chapters FOR SELECT USING (auth.role() = 'authenticated');

DROP POLICY IF EXISTS "Allow read access to all authenticated users" ON public.story_agents;
CREATE POLICY "Allow read access to all authenticated users" ON public.story_agents FOR SELECT USING (auth.role() = 'authenticated');
DROP POLICY IF EXISTS "Allow insert for authenticated users" ON public.story_agents;
CREATE POLICY "Allow insert for authenticated users" ON public.story_agents FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- 新表策略
DROP POLICY IF EXISTS "Allow individual access on chats" ON public.chats;
CREATE POLICY "Allow individual access on chats" ON public.chats FOR ALL USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Allow access to participants of own chats" ON public.chat_participants;
CREATE POLICY "Allow access to participants of own chats" ON public.chat_participants FOR ALL USING ((SELECT user_id FROM public.chats WHERE id = chat_id) = auth.uid());

DROP POLICY IF EXISTS "Allow access to messages of own chats" ON public.messages;
CREATE POLICY "Allow access to messages of own chats" ON public.messages FOR ALL USING ((SELECT user_id FROM public.chats WHERE id = chat_id) = auth.uid());

DROP POLICY IF EXISTS "Allow individual access to own memories" ON public.agent_memories;
CREATE POLICY "Allow individual access to own memories" ON public.agent_memories FOR ALL USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);

-- 新表的RLS策略
DROP POLICY IF EXISTS "Allow access to summaries of own chats" ON public.chat_summaries;
CREATE POLICY "Allow access to summaries of own chats" ON public.chat_summaries FOR ALL USING ((SELECT user_id FROM public.chats WHERE id = chat_id) = auth.uid());

DROP POLICY IF EXISTS "Allow read access for public and own world_infos" ON public.world_infos;
CREATE POLICY "Allow read access for public and own world_infos" ON public.world_infos FOR SELECT USING ((is_public = true) OR (auth.uid() = user_id));
DROP POLICY IF EXISTS "Allow individual access to own world_infos" ON public.world_infos;
CREATE POLICY "Allow individual access to own world_infos" ON public.world_infos FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Allow individual update access to own world_infos" ON public.world_infos FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Allow individual delete access to own world_infos" ON public.world_infos FOR DELETE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Allow access to entries of accessible world_infos" ON public.world_info_entries;
CREATE POLICY "Allow access to entries of accessible world_infos" ON public.world_info_entries FOR ALL USING ((SELECT user_id FROM public.world_infos WHERE id = world_info_id) = auth.uid() OR (SELECT is_public FROM public.world_infos WHERE id = world_info_id) = true);

DROP POLICY IF EXISTS "Allow access to agent world_info associations" ON public.agent_world_infos;
CREATE POLICY "Allow access to agent world_info associations" ON public.agent_world_infos FOR ALL USING ((SELECT user_id FROM public.agents WHERE id = agent_id) = auth.uid());

DROP POLICY IF EXISTS "Allow access to agent mode configs" ON public.agent_mode_configs;
CREATE POLICY "Allow access to agent mode configs" ON public.agent_mode_configs FOR ALL USING ((SELECT user_id FROM public.agents WHERE id = agent_id) = auth.uid());

-- =================================================================
-- 7. 性能优化：索引
-- =================================================================
CREATE INDEX IF NOT EXISTS idx_agents_user_id ON public.agents(user_id);
CREATE INDEX IF NOT EXISTS idx_stories_user_id ON public.stories(user_id);
CREATE INDEX IF NOT EXISTS idx_stories_tags ON public.stories USING gin(tags);
CREATE INDEX IF NOT EXISTS idx_story_chapters_story_id ON public.story_chapters(story_id);
CREATE INDEX IF NOT EXISTS idx_story_agents_story_id ON public.story_agents(story_id);
CREATE INDEX IF NOT EXISTS idx_story_agents_agent_id ON public.story_agents(agent_id);
CREATE INDEX IF NOT EXISTS idx_chats_user_id ON public.chats(user_id);
CREATE INDEX IF NOT EXISTS idx_messages_chat_id_created_at ON public.messages(chat_id, created_at);
CREATE INDEX IF NOT EXISTS idx_agent_memories_user_id_agent_id ON public.agent_memories(user_id, agent_id);

-- RAG相关索引
CREATE INDEX IF NOT EXISTS idx_messages_embedding ON public.messages USING hnsw (embedding vector_l2_ops);
CREATE INDEX IF NOT EXISTS idx_chat_summaries_chat_id ON public.chat_summaries(chat_id);

-- 世界书相关索引
CREATE INDEX IF NOT EXISTS idx_world_infos_user_id ON public.world_infos(user_id);
CREATE INDEX IF NOT EXISTS idx_world_info_entries_world_info_id ON public.world_info_entries(world_info_id);
CREATE INDEX IF NOT EXISTS idx_world_info_entries_keywords ON public.world_info_entries USING gin(keywords);
CREATE INDEX IF NOT EXISTS idx_agent_world_infos_agent_id ON public.agent_world_infos(agent_id);

-- 模式配置相关索引
CREATE INDEX IF NOT EXISTS idx_agent_mode_configs_agent_id_mode ON public.agent_mode_configs(agent_id, mode);


-- =================================================================
-- 8. 排行榜函数
-- =================================================================

-- 获取热门故事榜单
DROP FUNCTION IF EXISTS public.get_story_rankings(text);
CREATE OR REPLACE FUNCTION public.get_story_rankings(p_period TEXT)
RETURNS TABLE (rank BIGINT, id UUID, cover_url TEXT, title VARCHAR, popularity BIGINT) AS $$
DECLARE
    time_interval INTERVAL;
BEGIN
    -- Set time interval based on period
    time_interval := CASE
        WHEN p_period = 'daily' THEN '1 day'
        WHEN p_period = 'weekly' THEN '7 days'
        WHEN p_period = 'monthly' THEN '1 month'
        ELSE '999 years' -- all time
    END;

    RETURN QUERY
    WITH recent_chats AS (
        SELECT
            c.story_id,
            COUNT(c.id) as chat_count
        FROM public.chats c
        WHERE c.story_id IS NOT NULL
          AND c.created_at >= NOW() - time_interval
        GROUP BY c.story_id
    )
    SELECT
        row_number() OVER (ORDER BY COALESCE(rc.chat_count, 0) DESC, s.popularity DESC) as rank,
        s.id,
        s.cover_image_url as cover_url,
        s.title,
        COALESCE(rc.chat_count, 0) as popularity
    FROM public.stories s
    LEFT JOIN recent_chats rc ON s.id = rc.story_id
    WHERE s.is_public = true
    ORDER BY popularity DESC, s.popularity DESC
    LIMIT 10;
END;
$$ LANGUAGE plpgsql;

-- 获取热门角色榜单
DROP FUNCTION IF EXISTS public.get_agent_rankings(text);
CREATE OR REPLACE FUNCTION public.get_agent_rankings(p_period TEXT)
RETURNS TABLE (rank BIGINT, id UUID, cover_url TEXT, title VARCHAR, popularity BIGINT) AS $$
DECLARE
    time_interval INTERVAL;
BEGIN
    -- Set time interval based on period
    time_interval := CASE
        WHEN p_period = 'daily' THEN '1 day'
        WHEN p_period = 'weekly' THEN '7 days'
        WHEN p_period = 'monthly' THEN '1 month'
        ELSE '999 years' -- all time
    END;

    RETURN QUERY
    WITH recent_chats AS (
        SELECT
            cp.agent_id,
            COUNT(c.id) as chat_count
        FROM public.chats c
        JOIN public.chat_participants cp ON c.id = cp.chat_id
        WHERE c.created_at >= NOW() - time_interval
          AND c.story_id IS NULL -- Only count roleplay chats, not story chats
        GROUP BY cp.agent_id
    )
    SELECT
        row_number() OVER (ORDER BY COALESCE(rc.chat_count, 0) DESC, a.dialogue_count DESC) as rank,
        a.id,
        a.image_url as cover_url,
        a.name as title,
        COALESCE(rc.chat_count, 0) as popularity
    FROM public.agents a
    LEFT JOIN recent_chats rc ON a.id = rc.agent_id
    WHERE a.is_public = true AND a.is_system_agent = false
    ORDER BY popularity DESC, a.dialogue_count DESC
    LIMIT 10;
END;
$$ LANGUAGE plpgsql;

-- =================================================================
-- 9. 织梦者引擎 - 章节推进函数
-- =================================================================
DROP FUNCTION IF EXISTS public.advance_to_next_chapter(uuid, uuid);
CREATE OR REPLACE FUNCTION public.advance_to_next_chapter(
    p_current_chat_id UUID,
    p_user_id UUID
)
RETURNS UUID AS $$
DECLARE
    v_story_id UUID;
    v_current_chapter_id UUID;
    v_current_chapter_number INT;
    v_next_chapter RECORD;
    v_new_chat_id UUID;
    v_story_agents UUID[];
    v_current_game_state JSONB;
    v_current_task_progress JSONB;
BEGIN
    -- 1. 根据当前chat_id找到故事ID和当前章节信息
    SELECT
        c.story_id,
        c.game_state,
        c.task_progress
    INTO v_story_id, v_current_game_state, v_current_task_progress
    FROM public.chats c
    WHERE c.id = p_current_chat_id AND c.user_id = p_user_id;

    IF v_story_id IS NULL THEN
        RAISE EXCEPTION '当前聊天并非故事模式或不属于该用户';
    END IF;

    -- 2. 从task_progress中获取当前章节ID
    v_current_chapter_id := (v_current_task_progress->>'current_chapter_id')::UUID;

    -- 如果没有current_chapter_id，则查找第一章
    IF v_current_chapter_id IS NULL THEN
        SELECT id, chapter_number INTO v_current_chapter_id, v_current_chapter_number
        FROM public.story_chapters
        WHERE story_id = v_story_id
        ORDER BY chapter_number ASC
        LIMIT 1;
    ELSE
        -- 获取当前章节号
        SELECT chapter_number INTO v_current_chapter_number
        FROM public.story_chapters
        WHERE id = v_current_chapter_id;
    END IF;

    IF v_current_chapter_number IS NULL THEN
        RAISE EXCEPTION '无法确定当前章节';
    END IF;

    -- 3. 找到下一章节（修复：查找比当前章节号大的最接近的章节）
    SELECT * INTO v_next_chapter
    FROM public.story_chapters
    WHERE story_id = v_story_id AND chapter_number > v_current_chapter_number
    ORDER BY chapter_number ASC
    LIMIT 1;

    -- 如果没有下一章，返回 NULL
    IF v_next_chapter IS NULL THEN
        RETURN NULL;
    END IF;

    -- 4. 获取故事的所有参与角色
    SELECT array_agg(agent_id) INTO v_story_agents
    FROM public.story_agents
    WHERE story_id = v_story_id;

    -- 5. 创建新的task_progress，重置为下一章的初始状态
    v_current_task_progress := jsonb_build_object(
        'current_chapter_id', v_next_chapter.id,
        'chapters', jsonb_build_object(
            v_next_chapter.id::text, jsonb_build_object(
                'progress', 0,
                'status', 'in_progress'
            )
        ),
        'opening_sequence_index', -1
    );

    -- 6. 创建一个新的聊天会话，继承游戏状态但重置任务进度
    INSERT INTO public.chats (user_id, story_id, game_state, task_progress)
    VALUES (p_user_id, v_story_id, v_current_game_state, v_current_task_progress)
    RETURNING id INTO v_new_chat_id;

    -- 7. 为新会话关联所有故事角色
    IF v_story_agents IS NOT NULL THEN
        INSERT INTO public.chat_participants (chat_id, agent_id)
        SELECT v_new_chat_id, unnest(v_story_agents);
    END IF;

    -- 8. 返回新的 chat_id
    RETURN v_new_chat_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 脚本结束 --