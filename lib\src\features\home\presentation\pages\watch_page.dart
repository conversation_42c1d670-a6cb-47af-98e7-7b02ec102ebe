// lib/src/features/home/<USER>/pages/watch_page.dart

import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:go_router/go_router.dart';
import 'package:xinglian/src/core/theme/app_colors.dart';
import 'package:xinglian/src/features/auth/bloc/auth_bloc.dart';
import 'package:xinglian/src/features/chat/repository/chat_repository.dart';
import 'package:xinglian/src/features/discovery/models/agent_model.dart';
import 'package:xinglian/src/features/home/<USER>/recommendation_bloc.dart';
import 'package:xinglian/src/features/home/<USER>/recommendation_models.dart';
import 'package:xinglian/src/features/home/<USER>/recommendation_repository.dart';
import 'package:xinglian/src/features/interactive_story/models/interactive_story_model.dart';
import 'package:xinglian/src/features/chat/presentation/widgets/story_intro_dialog.dart';

class WatchPage extends StatelessWidget {
  const WatchPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => RecommendationBloc(
        context.read<RecommendationRepository>(),
      )..add(LoadInitialRecommendations()),
      child: Scaffold(
        backgroundColor: AppColors.background,
        body: BlocBuilder<RecommendationBloc, RecommendationState>(
          builder: (context, state) {
            if (state is RecommendationLoading || state is RecommendationInitial) {
              return const Center(child: CircularProgressIndicator(color: AppColors.accentPurple));
            }
            if (state is RecommendationError) {
              return Center(child: Text(state.message, style: const TextStyle(color: Colors.red)));
            }
            if (state is RecommendationLoaded) {
              final firstAgentItem = state.feedItems.firstWhere(
                (item) => item.type == RecommendationItemType.agent,
                orElse: () => const RecommendationItem(type: RecommendationItemType.agent, data: null),
              );

              final List<Widget> gridWidgets = [
                _buildVerticalFunctionCard(
                  context,
                  '宿命回响',
                  '多个TA同时回应你',
                  Icons.nightlight_round,
                  LinearGradient(
                    colors: [
                      const Color(0xFFFCE4EC).withOpacity(0.6),
                      const Color(0xFFF8BBD0).withOpacity(0.5),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
                
                if (firstAgentItem.data != null)
                  _buildRecommendationCard(firstAgentItem, context),
                
                 _buildVerticalFunctionCard(
                  context,
                  '印象匹配',
                  '千种印象任你选',
                  Icons.chat_bubble_outline,
                  LinearGradient(
                    colors: [
                      const Color(0xFFE1F5FE).withOpacity(0.6),
                      const Color(0xFFB3E5FC).withOpacity(0.5),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
                _buildVerticalFunctionCard(
                  context,
                  '自由创建',
                  '自定义你的专属恋人',
                  Icons.favorite_border,
                  LinearGradient(
                    colors: [
                      const Color(0xFFD4C2FC).withOpacity(0.6),
                      const Color(0xFFBCA2F7).withOpacity(0.5),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  onTap: () => context.push('/create-agent'),
                ),
                 _buildDailyFreeCard(context),

                ...state.feedItems
                    .where((item) => item != firstAgentItem)
                    .map((item) => _buildRecommendationCard(item, context))
                    .toList(),
              ];

              return CustomScrollView(
                slivers: [
                  _buildSliverAppBar(context),
                  const SliverToBoxAdapter(child: _BannerSection()),
                  _buildCombinedGridSliver(gridWidgets, context),
                ],
              );
            }
            return const SizedBox.shrink();
          },
        ),
      ),
    );
  }
}

Widget _buildCombinedGridSliver(List<Widget> items, BuildContext context) {
  return SliverPadding(
    padding: const EdgeInsets.fromLTRB(16, 16, 16, 100),
    sliver: SliverMasonryGrid.count(
      crossAxisCount: 2,
      mainAxisSpacing: 8,
      crossAxisSpacing: 8,
      childCount: items.length,
      itemBuilder: (context, index) {
        return items[index];
      },
    ),
  );
}

Widget _buildSliverAppBar(BuildContext context) {
  return SliverAppBar(
    backgroundColor: AppColors.background,
    elevation: 0,
    pinned: true,
    titleSpacing: 0,
    title: Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Row(
        children: [
          _buildAppTitle(),
          const SizedBox(width: 12),
          Expanded(
            child: GestureDetector(
              onTap: () => context.push('/search'),
              child: Container(
                height: 36,
                padding: const EdgeInsets.symmetric(horizontal: 12),
                decoration: BoxDecoration(
                  color: AppColors.inputBackground.withOpacity(0.8),
                  borderRadius: BorderRadius.circular(18),
                ),
                child: const Row(
                  children: [
                    Icon(Icons.search, color: AppColors.secondaryText, size: 20),
                    SizedBox(width: 8),
                    Text('角色/邀请码/兑换码', style: TextStyle(color: AppColors.secondaryText, fontSize: 14)),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: AppColors.inputBackground.withOpacity(0.8),
              borderRadius: BorderRadius.circular(18),
            ),
            child: const Row(
              children: [
                Icon(Icons.flash_on, color: AppColors.accentYellow, size: 16),
                SizedBox(width: 4),
                Text('免费能量', style: TextStyle(color: Colors.white, fontSize: 12)),
              ],
            ),
          ),
        ],
      ),
    ),
  );
}

class _BannerSection extends StatelessWidget {
  const _BannerSection();
  @override
  Widget build(BuildContext context) {
    final banner = {
        'image': 'https://i.imgur.com/your_banner_image_1.jpeg', 
        'title': '卡显影UP池',
        'subtitle': '拾光之约周边上线 全服10抽免费送!',
      };

    return Container(
      height: 150,
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 0),
      child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              image: DecorationImage(
                image: NetworkImage(banner['image']!),
                fit: BoxFit.cover,
                onError: (exception, stackTrace) => {},
              ),
            ),
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                gradient: LinearGradient(
                  colors: [ Colors.black.withOpacity(0.0), Colors.black.withOpacity(0.6) ],
                  begin: Alignment.center,
                  end: Alignment.bottomCenter,
                ),
              ),
              padding: const EdgeInsets.all(16),
              alignment: Alignment.bottomLeft,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text( banner['title']!, style: const TextStyle( color: Colors.white, fontSize: 18, fontWeight: FontWeight.bold, shadows: [Shadow(blurRadius: 4, color: Colors.black54)])),
                  Text( banner['subtitle']!, style: const TextStyle( color: Colors.white, fontSize: 12, shadows: [Shadow(blurRadius: 4, color: Colors.black54)])),
                ],
              ),
            ),
          )
    );
  }
}

Widget _buildVerticalFunctionCard(BuildContext context, String title, String subtitle, IconData icon, Gradient gradient, {VoidCallback? onTap}) {
  final Color glowColor = (gradient.colors.first).withOpacity(0.7);

  return GestureDetector(
    onTap: onTap,
    child: ClipRRect(
      borderRadius: BorderRadius.circular(16.0),
      child: Stack(
        children: [
          Positioned(
            bottom: -15, 
            left: 0,
            right: 0,
            height: 40,
            child: Container(
              decoration: BoxDecoration(
                gradient: RadialGradient(
                  colors: [
                    glowColor,
                    glowColor.withOpacity(0.0),
                  ],
                  stops: const [0.0, 0.6], 
                ),
              ),
            ),
          ),
          BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                gradient: gradient,
                border: Border.all(color: Colors.white.withOpacity(0.1)),
              ),
              child: 
                  Row(
                children: [
                  CircleAvatar(
                    radius: 16,
                    backgroundColor: Colors.white.withOpacity(0.1),
                    child: Icon(icon, color: Colors.white, size: 18),
                  ),
                  const SizedBox(width: 10),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(title, style: const TextStyle(color: Colors.white, fontSize: 14, fontWeight: FontWeight.w500)),
                        Text(subtitle, style: const TextStyle(color: AppColors.secondaryText, fontSize: 10)),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    ),
  );
}

Widget _buildDailyFreeCard(BuildContext context) {
  final Color glowColor = Colors.white.withOpacity(0.4); 

  return GestureDetector(
    onTap: () {},
    child: ClipRRect(
      borderRadius: BorderRadius.circular(16.0),
      child: Stack(
        children: [
          Positioned(
            bottom: -15,
            left: 0,
            right: 0,
            height: 40,
            child: Container(
              decoration: BoxDecoration(
                gradient: RadialGradient(
                  colors: [
                    glowColor,
                    glowColor.withOpacity(0.0),
                  ],
                  stops: const [0.0, 0.6],
                ),
              ),
            ),
          ),
          
          BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.white.withOpacity(0.25),
                    Colors.white.withOpacity(0.15),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                border: Border.all(color: Colors.white.withOpacity(0.25)),
              ),
              child: Row(
                children: [
                  const CircleAvatar(
                    radius: 16,
                    backgroundColor: AppColors.secondaryBg,
                    backgroundImage: NetworkImage('https://i.imgur.com/example_avatar_daily.png'),
                  ),
                  const SizedBox(width: 10),
                  const Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('每日限免', style: TextStyle(color: Colors.white, fontSize: 14, fontWeight: FontWeight.w500)),
                        Text('0点刷新', style: TextStyle(color: AppColors.secondaryText, fontSize: 10)),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    ),
  );
}

Widget _buildRecommendationCard(RecommendationItem item, BuildContext context) {
  String title = '未知';
  String? coverUrl;
  List<String> tags = [];
  String popularity = '0';
  String description = '...';
  int episodeCount = 0; // 新增变量

  if (item.type == RecommendationItemType.agent) {
    final agent = item.data as Agent;
    title = agent.name;
    coverUrl = agent.imageUrl;
    tags = agent.tags;
    popularity = _formatCount(agent.dialogueCount);
    description = agent.openingLine ?? agent.description;
  } else {
    final story = item.data as InteractiveStoryCard;
    title = story.title;
    coverUrl = story.coverUrl;
    popularity = _formatCount(story.viewCount);
    description = story.description ?? '...';
    episodeCount = story.episodeCount; // 获取章节数
  }

  return InkWell(
    onTap: () async {
      final authState = context.read<AuthBloc>().state;
      String? userId;
      if (authState is Authenticated) {
        userId = authState.userId;
      }

      if (userId == null) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('无法获取用户信息，请稍后重试')),
          );
        }
        return;
      }
      
      final chatRepository = context.read<ChatRepository>();

      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(child: CircularProgressIndicator()),
      );

      try {
        if (item.type == RecommendationItemType.agent) {
          final agent = item.data as Agent;
          final chatId = await chatRepository.startAgentChat(agent.id, userId);
          
          // --- ▼▼▼ 核心修正 ▼▼▼
          if (!context.mounted) return;
          Navigator.of(context, rootNavigator: true).pop(); // 关闭加载
          await Future.delayed(Duration.zero); // 等待下一帧
          if (!context.mounted) return;
          // --- ▲▲▲ 核心修正 ▲▲▲

          if (chatId != null) {
             context.push('/chat/$chatId');
          } else {
            throw Exception('未能开启与角色的对话');
          }
        } else { // 故事类型
          final story = item.data as InteractiveStoryCard;
          final chatId = await chatRepository.startStory(story.id, userId);
          
          if (chatId == null) {
            // --- ▼▼▼ 核心修正 ▼▼▼
            if (!context.mounted) return;
            Navigator.of(context, rootNavigator: true).pop(); // 关闭加载
             // --- ▲▲▲ 核心修正 ▲▲▲
            throw Exception('未能开启故事');
          }

          final chatDetails = await chatRepository.getChatSessionDetails(chatId);
          // --- ▼▼▼ 核心修正 ▼▼▼
          if (!context.mounted) return;
          Navigator.of(context, rootNavigator: true).pop(); // 关闭加载
           // --- ▲▲▲ 核心修正 ▲▲▲

          final taskProgress = chatDetails?['task_progress'] as Map<String, dynamic>?;
          final openingSequenceIndex = taskProgress?['opening_sequence_index'] as int? ?? 0;
          final hasProgress = openingSequenceIndex > 0;

          if (hasProgress) {
            if (context.mounted) {
                await Future.delayed(Duration.zero);
                if (context.mounted) context.push('/chat/$chatId');
            }
          } else {
            if (context.mounted) {
              final shouldStart = await StoryIntroDialog.show(
                context,
                storyTitle: story.title,
                storyDescription: story.description ?? '暂无描述',
              );

              if (shouldStart == true && context.mounted) {
                await Future.delayed(Duration.zero);
                if (context.mounted) context.push('/chat/$chatId');
              }
            }
          }
        }
      } catch (e) {
        // --- ▼▼▼ 核心修正 ▼▼▼
        if (context.mounted) {
          Navigator.of(context, rootNavigator: true).pop(); // 确保在出错时也关闭加载
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('操作失败: ${e.toString().replaceAll('Exception: ', '')}')),
          );
        }
        // --- ▲▲▲ 核心修正 ▲▲▲
      }
    },
    borderRadius: BorderRadius.circular(12),
    child: ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Stack(
            children: [
              AspectRatio(
                aspectRatio: 0.75,
                child: Image.network(
                  coverUrl ?? '',
                  fit: BoxFit.cover,
                  errorBuilder: (_, __, ___) => Container(
                    color: AppColors.secondaryBg,
                    child: const Icon(Icons.broken_image, color: AppColors.tertiaryText),
                  ),
                ),
              ),
              Positioned(
                bottom: 8,
                left: 8,
                right: 8,
                child: Wrap(
                  spacing: 6,
                  runSpacing: 4,
                  children: tags.take(2).map((tag) => Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.5),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(tag, style: const TextStyle(color: Colors.white, fontSize: 10)),
                  )).toList(),
                ),
              ),
              // --- ▼▼▼ 新增代码开始 ▼▼▼ ---
              if (item.type == RecommendationItemType.interactiveStory && episodeCount > 0)
                Positioned(
                  top: 8,
                  right: 8,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppColors.accentPurple.withOpacity(0.8), // 紫色背景
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '共$episodeCount集',
                      style: const TextStyle(
                        color: Colors.white, 
                        fontSize: 10, 
                        fontWeight: FontWeight.bold
                      ), // 白字
                    ),
                  ),
                ),
              // --- ▲▲▲ 新增代码结束 ▲▲▲ ---
            ],
          ),
          Container(
            padding: const EdgeInsets.all(12),
            color: AppColors.secondaryBg, 
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      child: Text(
                        title,
                        style: const TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.bold),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(Icons.local_fire_department_outlined, color: AppColors.secondaryText, size: 14),
                        const SizedBox(width: 4),
                        Text(popularity, style: const TextStyle(color: AppColors.secondaryText, fontSize: 12)),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 6),
                Text(
                  description,
                  style: const TextStyle(color: AppColors.secondaryText, fontSize: 12),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    ),
  );
}

// 格式化数字的辅助函数保持不变
String _formatCount(int count) {
  if (count >= 10000) {
    return '${(count / 10000).toStringAsFixed(1)}万';
  }
  return count.toString();
}

/// 构建带有辉光效果的 "星恋" 应用标题
Widget _buildAppTitle() {
  return Row(
    mainAxisSize: MainAxisSize.min, // 让 Row 包裹内容，不额外占用空间
    crossAxisAlignment: CrossAxisAlignment.center, // 确保图标和文字垂直居中
    children: [
      // 左侧的 Sparkle Icon
      Icon(
        Icons.auto_awesome, // 闪耀图标
        color: Colors.white.withOpacity(0.9),
        size: 22,
        shadows: [
          Shadow(
            color: Colors.white.withOpacity(0.5),
            blurRadius: 10.0,
          ),
        ],
      ),
      const SizedBox(width: 6), // 图标和文字的间距

      // "星恋" 错落文字
      Row(
        crossAxisAlignment: CrossAxisAlignment.baseline,
        textBaseline: TextBaseline.alphabetic,
        children: [
          Text(
            '星',
            style: TextStyle(
              fontFamily: 'BaiWuChangKeKeTi', // <--- 在这里应用字体
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
              shadows: [
                Shadow(
                  color: Colors.white.withOpacity(0.7),
                  blurRadius: 18.0,
                ),
                Shadow(
                  color: Colors.white.withOpacity(0.4),
                  blurRadius: 28.0,
                ),
              ],
            ),
          ),
          const SizedBox(width: 2),
          Text(
            '恋',
            style: TextStyle(
              fontFamily: 'BaiWuChangKeKeTi', // <--- 在这里应用字体
              fontSize: 18, // “恋”字较小
              fontWeight: FontWeight.bold,
              color: Colors.white,
              shadows: [
                Shadow(
                  color: Colors.white.withOpacity(0.6),
                  blurRadius: 12.0,
                ),
              ],
            ),
          ),
        ],
      ),
      const SizedBox(width: 4),
    ],
  );
}